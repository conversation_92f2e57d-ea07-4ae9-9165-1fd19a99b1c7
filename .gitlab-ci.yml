variables:
  GIT_SSL_NO_VERIFY: "true"
  CI_SERVER_TLS_CA_FILE: ""
  GIT_STRATEGY: clone
  TIMESTAMP: ${CI_PIPELINE_CREATED_AT}
  BUILDDIR: "C:/temp/build-${CI_PIPELINE_ID}"
  DEFAULT_VERSION: "dev"
  PROJECT_NAME: "fxethercatsdk"

build:
  tags:
    - win  # Windows runner 标签
  rules:
    - if: $CI_COMMIT_TAG
      when: always
    - when: never

  cache:
    key: "$CI_COMMIT_REF_NAME"
    paths:
      - .nuget/packages/

  artifacts:
    name: "${PROJECT_NAME}-${CI_COMMIT_TAG}-${CI_COMMIT_SHORT_SHA}-${TIMESTAMP}"
    paths:
      - "*.zip"
    expire_in: 30 days

  before_script:
    - '[Console]::OutputEncoding = [System.Text.Encoding]::UTF8'
    - 'chcp 65001'

  script:
    - Write-Host "🔧 创建构建目录：$env:BUILDDIR"
    - New-Item -ItemType Directory -Force -Path "$env:BUILDDIR" | Out-Null
    - Copy-Item -Recurse -Force * "$env:BUILDDIR"
    - Set-Location "$env:BUILDDIR"
    - |
      # 获取版本号
      if ($env:CI_COMMIT_TAG) {
        $global:version = $env:CI_COMMIT_TAG -replace "^v", ""
      } else {
        $global:version = "$env:DEFAULT_VERSION"
      }
      Write-Host "📦 使用版本号：$global:version"

      # 修改 .csproj 文件中的版本号
      $csproj = "SDK/FXEtherCATSDKWrapper/FXEtherCATSDKWrapper.csproj"
      Write-Host "🔍 当前工作目录：$(Get-Location)"
      Write-Host "🔍 检查文件路径：$csproj"

      if (!(Test-Path $csproj)) {
        Write-Error "❌ 找不到 $csproj"
        Get-ChildItem -Recurse -Name "*.csproj" | Write-Host
        exit 1
      }

      [xml]$projXml = Get-Content $csproj
      $props = $projXml.Project.PropertyGroup | Where-Object { $_.Version -or $_.AssemblyVersion -or $_.FileVersion }
      if ($props) {
        $props.Version = $global:version
        $props.AssemblyVersion = $global:version
        $props.FileVersion = $global:version
        $projXml.Save((Get-Item $csproj).FullName)
        Write-Host "✅ 已更新 $csproj 版本号为 $global:version"
      } else {
        Write-Warning "⚠️ 未找到版本属性节点"
      }
    - Write-Host "🔨 开始构建 FXEtherCATSDKWrapper DLL..."
    - Set-Location "SDK/FXEtherCATSDKWrapper"
    - dotnet restore
    - |
      # 定义目标平台 (仅Linux)
      $platforms = @(
        @{Name="linux-x64"; DisplayName="Linux x64"},
        @{Name="linux-arm64"; DisplayName="Linux ARM64"},
        @{Name="linux-arm"; DisplayName="Linux ARM32"}
      )

      Write-Host "🏗️ 开始多平台构建..."
      foreach ($platform in $platforms) {
        Write-Host "📦 构建 $($platform.DisplayName) 版本..."
        dotnet build --configuration Release --runtime $($platform.Name) --self-contained false
        if ($LASTEXITCODE -ne 0) {
          Write-Error "❌ $($platform.DisplayName) 构建失败"
          exit 1
        }
        Write-Host "✅ $($platform.DisplayName) 构建成功"
      }
    - Set-Location "$env:BUILDDIR"
    - |
      # 创建发布包目录
      $packageDir = "package"
      New-Item -ItemType Directory -Force -Path $packageDir | Out-Null
      Write-Host "📁 创建发布包目录：$packageDir"
    - |
      # 拷贝编译好的多平台 DLL (仅Linux)
      $platforms = @(
        @{Name="linux-x64"; DisplayName="Linux x64"; Folder="linux-x64"},
        @{Name="linux-arm64"; DisplayName="Linux ARM64"; Folder="linux-arm64"},
        @{Name="linux-arm"; DisplayName="Linux ARM32"; Folder="linux-arm"}
      )

      foreach ($platform in $platforms) {
        $dllSource = "SDK/FXEtherCATSDKWrapper/bin/Release/net8.0/$($platform.Folder)/FXEtherCATSDKWrapper.dll"
        $targetDir = "$packageDir/$($platform.Folder)"

        if (Test-Path $dllSource) {
          New-Item -ItemType Directory -Force -Path $targetDir | Out-Null
          Copy-Item $dllSource $targetDir
          Write-Host "✅ 已拷贝 $($platform.DisplayName) DLL: $dllSource"
        } else {
          Write-Warning "⚠️ 找不到 $($platform.DisplayName) DLL: $dllSource"
          # 列出实际存在的文件以便调试
          Write-Host "🔍 实际存在的文件:"
          Get-ChildItem "SDK/FXEtherCATSDKWrapper/bin/Release/" -Recurse -Name "*.dll" | ForEach-Object { Write-Host "  $_" }
        }
      }
    - |
      # 拷贝 MD 文档文件
      $mdFiles = @(
        "SDK/FXEtherCATSDK_User_Guide.md",
        "SDK/FXEtherCAT_SDK_API_Documentation.md"
      )
      foreach ($mdFile in $mdFiles) {
        if (Test-Path $mdFile) {
          Copy-Item $mdFile "$packageDir/"
          Write-Host "✅ 已拷贝文档: $mdFile"
        } else {
          Write-Warning "⚠️ 找不到文档文件: $mdFile"
        }
      }
    - |
      # 拷贝 Demo 文件夹
      if (Test-Path "Demo") {
        Copy-Item -Recurse "Demo" "$packageDir/"
        Write-Host "✅ 已拷贝 Demo 文件夹"
      } else {
        Write-Warning "⚠️ 找不到 Demo 文件夹"
      }
    - |
      # 创建压缩包
      $timestamp = $env:TIMESTAMP -replace ":", "-" -replace "T", "_"
      $zipName = "$env:PROJECT_NAME-$env:CI_COMMIT_TAG-$env:CI_COMMIT_SHORT_SHA-$timestamp.zip"
      Write-Host "📦 创建压缩包: $zipName"
      Compress-Archive -Path "$packageDir/*" -DestinationPath $zipName -Force

      if (Test-Path $zipName) {
        Write-Host "✅ 压缩包创建成功: $zipName"
        $zipSize = (Get-Item $zipName).Length / 1MB
        Write-Host "📊 压缩包大小: $([math]::Round($zipSize, 2)) MB"

        # 将压缩包拷贝回原始工作目录，以便GitLab CI可以找到它作为artifact
        $originalWorkDir = $env:CI_PROJECT_DIR
        if (!$originalWorkDir) {
          $originalWorkDir = "C:\gitlab-runner\builds\zv6Ry27Ro\0\fxos-ipc\fxethercatsdk"
        }
        Copy-Item $zipName $originalWorkDir
        Write-Host "📤 已将压缩包拷贝到原始工作目录: $originalWorkDir"
      } else {
        Write-Error "❌ 压缩包创建失败"
        exit 1
      }
