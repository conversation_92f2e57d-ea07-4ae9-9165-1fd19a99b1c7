{"slaves": [{"dc_config": {"assign_activate": "0x0300", "sync0_cycle": "", "sync0_shift": "", "sync1_cycle": "", "sync1_shift": ""}, "index": "0", "name": "L5N(COE)", "pid": "0x000000f0", "rx_pdo": "0x1600", "rx_pdos": [{"bitlen": 8, "comment": "操作模式设置", "index": "0x6060", "name": "operation_mode", "subindex": "0", "type": "uint8"}, {"bitlen": 32, "comment": "目标速度", "index": "0x60FF", "name": "target_speed", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "控制字", "index": "0x6040", "name": "control_word", "subindex": "0", "type": "uint16"}], "tx_pdo": "0x1a00", "tx_pdos": [{"bitlen": 16, "comment": "状态字", "index": "0x6041", "name": "status_word", "subindex": "0", "type": "uint16"}, {"bitlen": 16, "comment": "错误代码", "index": "0x603F", "name": "error_code", "subindex": "0", "type": "uint16"}, {"bitlen": 32, "comment": "实际反馈速度", "index": "0x606C", "name": "actual_velocity", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "厂商错误码", "index": "0x503F", "name": "customize_error_code", "subindex": "0", "type": "uint16"}], "vid": "0x00004321"}, {"index": "1", "name": "L5N(COE)", "pid": "0x000000f0", "rx_pdo": "0x1600", "rx_pdos": [{"bitlen": 8, "comment": "操作模式设置", "index": "0x6060", "name": "operation_mode", "subindex": "0", "type": "uint8"}, {"bitlen": 32, "comment": "目标速度", "index": "0x60FF", "name": "target_speed", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "控制字", "index": "0x6040", "name": "control_word", "subindex": "0", "type": "uint16"}], "tx_pdo": "0x1a00", "tx_pdos": [{"bitlen": 16, "comment": "状态字", "index": "0x6041", "name": "status_word", "subindex": "0", "type": "uint16"}, {"bitlen": 16, "comment": "错误代码", "index": "0x603F", "name": "error_code", "subindex": "0", "type": "uint16"}, {"bitlen": 32, "comment": "实际反馈速度", "index": "0x606C", "name": "actual_velocity", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "厂商错误码", "index": "0x503F", "name": "customize_error_code", "subindex": "0", "type": "uint16"}], "vid": "0x00004321", "dc_config": {"assign_activate": "0x0300", "sync0_cycle": "", "sync0_shift": "", "sync1_cycle": "", "sync1_shift": ""}}, {"index": "2", "name": "L5N(COE)", "pid": "0x000000f0", "rx_pdo": "0x1600", "rx_pdos": [{"bitlen": 8, "comment": "操作模式设置", "index": "0x6060", "name": "operation_mode", "subindex": "0", "type": "uint8"}, {"bitlen": 32, "comment": "目标速度", "index": "0x60FF", "name": "target_speed", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "控制字", "index": "0x6040", "name": "control_word", "subindex": "0", "type": "uint16"}], "tx_pdo": "0x1a00", "tx_pdos": [{"bitlen": 16, "comment": "状态字", "index": "0x6041", "name": "status_word", "subindex": "0", "type": "uint16"}, {"bitlen": 16, "comment": "错误代码", "index": "0x603F", "name": "error_code", "subindex": "0", "type": "uint16"}, {"bitlen": 32, "comment": "实际反馈速度", "index": "0x606C", "name": "actual_velocity", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "厂商错误码", "index": "0x503F", "name": "customize_error_code", "subindex": "0", "type": "uint16"}], "vid": "0x00004321", "dc_config": {"assign_activate": "0x0300", "sync0_cycle": "", "sync0_shift": "", "sync1_cycle": "", "sync1_shift": ""}}, {"index": "3", "name": "L5N(COE)", "pid": "0x000000f0", "rx_pdo": "0x1600", "rx_pdos": [{"bitlen": 8, "comment": "操作模式设置", "index": "0x6060", "name": "operation_mode", "subindex": "0", "type": "uint8"}, {"bitlen": 32, "comment": "目标速度", "index": "0x60FF", "name": "target_speed", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "控制字", "index": "0x6040", "name": "control_word", "subindex": "0", "type": "uint16"}], "tx_pdo": "0x1a00", "tx_pdos": [{"bitlen": 16, "comment": "状态字", "index": "0x6041", "name": "status_word", "subindex": "0", "type": "uint16"}, {"bitlen": 16, "comment": "错误代码", "index": "0x603F", "name": "error_code", "subindex": "0", "type": "uint16"}, {"bitlen": 32, "comment": "实际反馈速度", "index": "0x606C", "name": "actual_velocity", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "厂商错误码", "index": "0x503F", "name": "customize_error_code", "subindex": "0", "type": "uint16"}], "vid": "0x00004321", "dc_config": {"assign_activate": "0x0300", "sync0_cycle": "", "sync0_shift": "", "sync1_cycle": "", "sync1_shift": ""}}, {"index": "4", "name": "L5N(COE)", "pid": "0x000000f0", "rx_pdo": "0x1600", "rx_pdos": [{"bitlen": 8, "comment": "操作模式设置", "index": "0x6060", "name": "operation_mode", "subindex": "0", "type": "uint8"}, {"bitlen": 32, "comment": "目标速度", "index": "0x60FF", "name": "target_speed", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "控制字", "index": "0x6040", "name": "control_word", "subindex": "0", "type": "uint16"}], "tx_pdo": "0x1a00", "tx_pdos": [{"bitlen": 16, "comment": "状态字", "index": "0x6041", "name": "status_word", "subindex": "0", "type": "uint16"}, {"bitlen": 16, "comment": "错误代码", "index": "0x603F", "name": "error_code", "subindex": "0", "type": "uint16"}, {"bitlen": 32, "comment": "实际反馈速度", "index": "0x606C", "name": "actual_velocity", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "厂商错误码", "index": "0x503F", "name": "customize_error_code", "subindex": "0", "type": "uint16"}], "vid": "0x00004321", "dc_config": {"assign_activate": "0x0300", "sync0_cycle": "", "sync0_shift": "", "sync1_cycle": "", "sync1_shift": ""}}, {"index": "5", "name": "L5N(COE)", "pid": "0x000000f0", "rx_pdo": "0x1600", "rx_pdos": [{"bitlen": 8, "comment": "操作模式设置", "index": "0x6060", "name": "operation_mode", "subindex": "0", "type": "uint8"}, {"bitlen": 32, "comment": "目标速度", "index": "0x60FF", "name": "target_speed", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "控制字", "index": "0x6040", "name": "control_word", "subindex": "0", "type": "uint16"}], "tx_pdo": "0x1a00", "tx_pdos": [{"bitlen": 16, "comment": "状态字", "index": "0x6041", "name": "status_word", "subindex": "0", "type": "uint16"}, {"bitlen": 16, "comment": "错误代码", "index": "0x603F", "name": "error_code", "subindex": "0", "type": "uint16"}, {"bitlen": 32, "comment": "实际反馈速度", "index": "0x606C", "name": "actual_velocity", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "厂商错误码", "index": "0x503F", "name": "customize_error_code", "subindex": "0", "type": "uint16"}], "vid": "0x00004321", "dc_config": {"assign_activate": "0x0300", "sync0_cycle": "", "sync0_shift": "", "sync1_cycle": "", "sync1_shift": ""}}, {"index": "6", "name": "L5N(COE)", "pid": "0x000000f0", "rx_pdo": "0x1600", "rx_pdos": [{"bitlen": 8, "comment": "操作模式设置", "index": "0x6060", "name": "operation_mode", "subindex": "0", "type": "uint8"}, {"bitlen": 32, "comment": "目标速度", "index": "0x60FF", "name": "target_speed", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "控制字", "index": "0x6040", "name": "control_word", "subindex": "0", "type": "uint16"}], "tx_pdo": "0x1a00", "tx_pdos": [{"bitlen": 16, "comment": "状态字", "index": "0x6041", "name": "status_word", "subindex": "0", "type": "uint16"}, {"bitlen": 16, "comment": "错误代码", "index": "0x603F", "name": "error_code", "subindex": "0", "type": "uint16"}, {"bitlen": 32, "comment": "实际反馈速度", "index": "0x606C", "name": "actual_velocity", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "厂商错误码", "index": "0x503F", "name": "customize_error_code", "subindex": "0", "type": "uint16"}], "vid": "0x00004321", "dc_config": {"assign_activate": "0x0300", "sync0_cycle": "", "sync0_shift": "", "sync1_cycle": "", "sync1_shift": ""}}, {"index": "7", "name": "L5N(COE)", "pid": "0x000000f0", "rx_pdo": "0x1600", "rx_pdos": [{"bitlen": 8, "comment": "操作模式设置", "index": "0x6060", "name": "operation_mode", "subindex": "0", "type": "uint8"}, {"bitlen": 32, "comment": "目标速度", "index": "0x60FF", "name": "target_speed", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "控制字", "index": "0x6040", "name": "control_word", "subindex": "0", "type": "uint16"}], "tx_pdo": "0x1a00", "tx_pdos": [{"bitlen": 16, "comment": "状态字", "index": "0x6041", "name": "status_word", "subindex": "0", "type": "uint16"}, {"bitlen": 16, "comment": "错误代码", "index": "0x603F", "name": "error_code", "subindex": "0", "type": "uint16"}, {"bitlen": 32, "comment": "实际反馈速度", "index": "0x606C", "name": "actual_velocity", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "厂商错误码", "index": "0x503F", "name": "customize_error_code", "subindex": "0", "type": "uint16"}], "vid": "0x00004321", "dc_config": {"assign_activate": "0x0300", "sync0_cycle": "", "sync0_shift": "", "sync1_cycle": "", "sync1_shift": ""}}, {"index": "8", "name": "L5N(COE)", "pid": "0x000000f0", "rx_pdo": "0x1600", "rx_pdos": [{"bitlen": 8, "comment": "操作模式设置", "index": "0x6060", "name": "operation_mode", "subindex": "0", "type": "uint8"}, {"bitlen": 32, "comment": "目标速度", "index": "0x60FF", "name": "target_speed", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "控制字", "index": "0x6040", "name": "control_word", "subindex": "0", "type": "uint16"}], "tx_pdo": "0x1a00", "tx_pdos": [{"bitlen": 16, "comment": "状态字", "index": "0x6041", "name": "status_word", "subindex": "0", "type": "uint16"}, {"bitlen": 16, "comment": "错误代码", "index": "0x603F", "name": "error_code", "subindex": "0", "type": "uint16"}, {"bitlen": 32, "comment": "实际反馈速度", "index": "0x606C", "name": "actual_velocity", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "厂商错误码", "index": "0x503F", "name": "customize_error_code", "subindex": "0", "type": "uint16"}], "vid": "0x00004321", "dc_config": {"assign_activate": "0x0300", "sync0_cycle": "", "sync0_shift": "", "sync1_cycle": "", "sync1_shift": ""}}, {"index": "9", "name": "L5N(COE)", "pid": "0x000000f0", "rx_pdo": "0x1600", "rx_pdos": [{"bitlen": 8, "comment": "操作模式设置", "index": "0x6060", "name": "operation_mode", "subindex": "0", "type": "uint8"}, {"bitlen": 32, "comment": "目标速度", "index": "0x60FF", "name": "target_speed", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "控制字", "index": "0x6040", "name": "control_word", "subindex": "0", "type": "uint16"}], "tx_pdo": "0x1a00", "tx_pdos": [{"bitlen": 16, "comment": "状态字", "index": "0x6041", "name": "status_word", "subindex": "0", "type": "uint16"}, {"bitlen": 16, "comment": "错误代码", "index": "0x603F", "name": "error_code", "subindex": "0", "type": "uint16"}, {"bitlen": 32, "comment": "实际反馈速度", "index": "0x606C", "name": "actual_velocity", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "厂商错误码", "index": "0x503F", "name": "customize_error_code", "subindex": "0", "type": "uint16"}], "vid": "0x00004321", "dc_config": {"assign_activate": "0x0300", "sync0_cycle": "", "sync0_shift": "", "sync1_cycle": "", "sync1_shift": ""}}, {"index": "10", "name": "L5N(COE)", "pid": "0x000000f0", "rx_pdo": "0x1600", "rx_pdos": [{"bitlen": 8, "comment": "操作模式设置", "index": "0x6060", "name": "operation_mode", "subindex": "0", "type": "uint8"}, {"bitlen": 32, "comment": "目标速度", "index": "0x60FF", "name": "target_speed", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "控制字", "index": "0x6040", "name": "control_word", "subindex": "0", "type": "uint16"}], "tx_pdo": "0x1a00", "tx_pdos": [{"bitlen": 16, "comment": "状态字", "index": "0x6041", "name": "status_word", "subindex": "0", "type": "uint16"}, {"bitlen": 16, "comment": "错误代码", "index": "0x603F", "name": "error_code", "subindex": "0", "type": "uint16"}, {"bitlen": 32, "comment": "实际反馈速度", "index": "0x606C", "name": "actual_velocity", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "厂商错误码", "index": "0x503F", "name": "customize_error_code", "subindex": "0", "type": "uint16"}], "vid": "0x00004321", "dc_config": {"assign_activate": "0x0300", "sync0_cycle": "", "sync0_shift": "", "sync1_cycle": "", "sync1_shift": ""}}, {"index": "11", "name": "L5N(COE)", "pid": "0x000000f0", "rx_pdo": "0x1600", "rx_pdos": [{"bitlen": 8, "comment": "操作模式设置", "index": "0x6060", "name": "operation_mode", "subindex": "0", "type": "uint8"}, {"bitlen": 32, "comment": "目标速度", "index": "0x60FF", "name": "target_speed", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "控制字", "index": "0x6040", "name": "control_word", "subindex": "0", "type": "uint16"}], "tx_pdo": "0x1a00", "tx_pdos": [{"bitlen": 16, "comment": "状态字", "index": "0x6041", "name": "status_word", "subindex": "0", "type": "uint16"}, {"bitlen": 16, "comment": "错误代码", "index": "0x603F", "name": "error_code", "subindex": "0", "type": "uint16"}, {"bitlen": 32, "comment": "实际反馈速度", "index": "0x606C", "name": "actual_velocity", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "厂商错误码", "index": "0x503F", "name": "customize_error_code", "subindex": "0", "type": "uint16"}], "vid": "0x00004321", "dc_config": {"assign_activate": "0x0300", "sync0_cycle": "", "sync0_shift": "", "sync1_cycle": "", "sync1_shift": ""}}, {"index": "12", "name": "L5N(COE)", "pid": "0x000000f0", "rx_pdo": "0x1600", "rx_pdos": [{"bitlen": 8, "comment": "操作模式设置", "index": "0x6060", "name": "operation_mode", "subindex": "0", "type": "uint8"}, {"bitlen": 32, "comment": "目标速度", "index": "0x60FF", "name": "target_speed", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "控制字", "index": "0x6040", "name": "control_word", "subindex": "0", "type": "uint16"}], "tx_pdo": "0x1a00", "tx_pdos": [{"bitlen": 16, "comment": "状态字", "index": "0x6041", "name": "status_word", "subindex": "0", "type": "uint16"}, {"bitlen": 16, "comment": "错误代码", "index": "0x603F", "name": "error_code", "subindex": "0", "type": "uint16"}, {"bitlen": 32, "comment": "实际反馈速度", "index": "0x606C", "name": "actual_velocity", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "厂商错误码", "index": "0x503F", "name": "customize_error_code", "subindex": "0", "type": "uint16"}], "vid": "0x00004321", "dc_config": {"assign_activate": "0x0300", "sync0_cycle": "", "sync0_shift": "", "sync1_cycle": "", "sync1_shift": ""}}, {"index": "13", "name": "L5N(COE)", "pid": "0x000000f0", "rx_pdo": "0x1600", "rx_pdos": [{"bitlen": 8, "comment": "操作模式设置", "index": "0x6060", "name": "operation_mode", "subindex": "0", "type": "uint8"}, {"bitlen": 32, "comment": "目标速度", "index": "0x60FF", "name": "target_speed", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "控制字", "index": "0x6040", "name": "control_word", "subindex": "0", "type": "uint16"}], "tx_pdo": "0x1a00", "tx_pdos": [{"bitlen": 16, "comment": "状态字", "index": "0x6041", "name": "status_word", "subindex": "0", "type": "uint16"}, {"bitlen": 16, "comment": "错误代码", "index": "0x603F", "name": "error_code", "subindex": "0", "type": "uint16"}, {"bitlen": 32, "comment": "实际反馈速度", "index": "0x606C", "name": "actual_velocity", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "厂商错误码", "index": "0x503F", "name": "customize_error_code", "subindex": "0", "type": "uint16"}], "vid": "0x00004321", "dc_config": {"assign_activate": "0x0300", "sync0_cycle": "", "sync0_shift": "", "sync1_cycle": "", "sync1_shift": ""}}, {"index": "14", "name": "L5N(COE)", "pid": "0x000000f0", "rx_pdo": "0x1600", "rx_pdos": [{"bitlen": 8, "comment": "操作模式设置", "index": "0x6060", "name": "operation_mode", "subindex": "0", "type": "uint8"}, {"bitlen": 32, "comment": "目标速度", "index": "0x60FF", "name": "target_speed", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "控制字", "index": "0x6040", "name": "control_word", "subindex": "0", "type": "uint16"}], "tx_pdo": "0x1a00", "tx_pdos": [{"bitlen": 16, "comment": "状态字", "index": "0x6041", "name": "status_word", "subindex": "0", "type": "uint16"}, {"bitlen": 16, "comment": "错误代码", "index": "0x603F", "name": "error_code", "subindex": "0", "type": "uint16"}, {"bitlen": 32, "comment": "实际反馈速度", "index": "0x606C", "name": "actual_velocity", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "厂商错误码", "index": "0x503F", "name": "customize_error_code", "subindex": "0", "type": "uint16"}], "vid": "0x00004321", "dc_config": {"assign_activate": "0x0300", "sync0_cycle": "", "sync0_shift": "", "sync1_cycle": "", "sync1_shift": ""}}, {"index": "15", "name": "L5N(COE)", "pid": "0x000000f0", "rx_pdo": "0x1600", "rx_pdos": [{"bitlen": 8, "comment": "操作模式设置", "index": "0x6060", "name": "operation_mode", "subindex": "0", "type": "uint8"}, {"bitlen": 32, "comment": "目标速度", "index": "0x60FF", "name": "target_speed", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "控制字", "index": "0x6040", "name": "control_word", "subindex": "0", "type": "uint16"}], "tx_pdo": "0x1a00", "tx_pdos": [{"bitlen": 16, "comment": "状态字", "index": "0x6041", "name": "status_word", "subindex": "0", "type": "uint16"}, {"bitlen": 16, "comment": "错误代码", "index": "0x603F", "name": "error_code", "subindex": "0", "type": "uint16"}, {"bitlen": 32, "comment": "实际反馈速度", "index": "0x606C", "name": "actual_velocity", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "厂商错误码", "index": "0x503F", "name": "customize_error_code", "subindex": "0", "type": "uint16"}], "vid": "0x00004321", "dc_config": {"assign_activate": "0x0300", "sync0_cycle": "", "sync0_shift": "", "sync1_cycle": "", "sync1_shift": ""}}, {"index": "16", "name": "L5N(COE)", "pid": "0x000000f0", "rx_pdo": "0x1600", "rx_pdos": [{"bitlen": 8, "comment": "操作模式设置", "index": "0x6060", "name": "operation_mode", "subindex": "0", "type": "uint8"}, {"bitlen": 32, "comment": "目标速度", "index": "0x60FF", "name": "target_speed", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "控制字", "index": "0x6040", "name": "control_word", "subindex": "0", "type": "uint16"}], "tx_pdo": "0x1a00", "tx_pdos": [{"bitlen": 16, "comment": "状态字", "index": "0x6041", "name": "status_word", "subindex": "0", "type": "uint16"}, {"bitlen": 16, "comment": "错误代码", "index": "0x603F", "name": "error_code", "subindex": "0", "type": "uint16"}, {"bitlen": 32, "comment": "实际反馈速度", "index": "0x606C", "name": "actual_velocity", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "厂商错误码", "index": "0x503F", "name": "customize_error_code", "subindex": "0", "type": "uint16"}], "vid": "0x00004321", "dc_config": {"assign_activate": "0x0300", "sync0_cycle": "", "sync0_shift": "", "sync1_cycle": "", "sync1_shift": ""}}, {"index": "17", "name": "L5N(COE)", "pid": "0x000000f0", "rx_pdo": "0x1600", "rx_pdos": [{"bitlen": 8, "comment": "操作模式设置", "index": "0x6060", "name": "operation_mode", "subindex": "0", "type": "uint8"}, {"bitlen": 32, "comment": "目标速度", "index": "0x60FF", "name": "target_speed", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "控制字", "index": "0x6040", "name": "control_word", "subindex": "0", "type": "uint16"}], "tx_pdo": "0x1a00", "tx_pdos": [{"bitlen": 16, "comment": "状态字", "index": "0x6041", "name": "status_word", "subindex": "0", "type": "uint16"}, {"bitlen": 16, "comment": "错误代码", "index": "0x603F", "name": "error_code", "subindex": "0", "type": "uint16"}, {"bitlen": 32, "comment": "实际反馈速度", "index": "0x606C", "name": "actual_velocity", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "厂商错误码", "index": "0x503F", "name": "customize_error_code", "subindex": "0", "type": "uint16"}], "vid": "0x00004321", "dc_config": {"assign_activate": "0x0300", "sync0_cycle": "", "sync0_shift": "", "sync1_cycle": "", "sync1_shift": ""}}, {"index": "18", "name": "L5N(COE)", "pid": "0x000000f0", "rx_pdo": "0x1600", "rx_pdos": [{"bitlen": 8, "comment": "操作模式设置", "index": "0x6060", "name": "operation_mode", "subindex": "0", "type": "uint8"}, {"bitlen": 32, "comment": "目标速度", "index": "0x60FF", "name": "target_speed", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "控制字", "index": "0x6040", "name": "control_word", "subindex": "0", "type": "uint16"}], "tx_pdo": "0x1a00", "tx_pdos": [{"bitlen": 16, "comment": "状态字", "index": "0x6041", "name": "status_word", "subindex": "0", "type": "uint16"}, {"bitlen": 16, "comment": "错误代码", "index": "0x603F", "name": "error_code", "subindex": "0", "type": "uint16"}, {"bitlen": 32, "comment": "实际反馈速度", "index": "0x606C", "name": "actual_velocity", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "厂商错误码", "index": "0x503F", "name": "customize_error_code", "subindex": "0", "type": "uint16"}], "vid": "0x00004321", "dc_config": {"assign_activate": "0x0300", "sync0_cycle": "", "sync0_shift": "", "sync1_cycle": "", "sync1_shift": ""}}, {"index": "19", "name": "L5N(COE)", "pid": "0x000000f0", "rx_pdo": "0x1600", "rx_pdos": [{"bitlen": 8, "comment": "操作模式设置", "index": "0x6060", "name": "operation_mode", "subindex": "0", "type": "uint8"}, {"bitlen": 32, "comment": "目标速度", "index": "0x60FF", "name": "target_speed", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "控制字", "index": "0x6040", "name": "control_word", "subindex": "0", "type": "uint16"}], "tx_pdo": "0x1a00", "tx_pdos": [{"bitlen": 16, "comment": "状态字", "index": "0x6041", "name": "status_word", "subindex": "0", "type": "uint16"}, {"bitlen": 16, "comment": "错误代码", "index": "0x603F", "name": "error_code", "subindex": "0", "type": "uint16"}, {"bitlen": 32, "comment": "实际反馈速度", "index": "0x606C", "name": "actual_velocity", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "厂商错误码", "index": "0x503F", "name": "customize_error_code", "subindex": "0", "type": "uint16"}], "vid": "0x00004321", "dc_config": {"assign_activate": "0x0300", "sync0_cycle": "", "sync0_shift": "", "sync1_cycle": "", "sync1_shift": ""}}, {"index": "20", "name": "L5N(COE)", "pid": "0x000000f0", "rx_pdo": "0x1600", "rx_pdos": [{"bitlen": 8, "comment": "操作模式设置", "index": "0x6060", "name": "operation_mode", "subindex": "0", "type": "uint8"}, {"bitlen": 32, "comment": "目标速度", "index": "0x60FF", "name": "target_speed", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "控制字", "index": "0x6040", "name": "control_word", "subindex": "0", "type": "uint16"}], "tx_pdo": "0x1a00", "tx_pdos": [{"bitlen": 16, "comment": "状态字", "index": "0x6041", "name": "status_word", "subindex": "0", "type": "uint16"}, {"bitlen": 16, "comment": "错误代码", "index": "0x603F", "name": "error_code", "subindex": "0", "type": "uint16"}, {"bitlen": 32, "comment": "实际反馈速度", "index": "0x606C", "name": "actual_velocity", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "厂商错误码", "index": "0x503F", "name": "customize_error_code", "subindex": "0", "type": "uint16"}], "vid": "0x00004321", "dc_config": {"assign_activate": "0x0300", "sync0_cycle": "", "sync0_shift": "", "sync1_cycle": "", "sync1_shift": ""}}, {"index": "21", "name": "L5N(COE)", "pid": "0x000000f0", "rx_pdo": "0x1600", "rx_pdos": [{"bitlen": 8, "comment": "操作模式设置", "index": "0x6060", "name": "operation_mode", "subindex": "0", "type": "uint8"}, {"bitlen": 32, "comment": "目标速度", "index": "0x60FF", "name": "target_speed", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "控制字", "index": "0x6040", "name": "control_word", "subindex": "0", "type": "uint16"}], "tx_pdo": "0x1a00", "tx_pdos": [{"bitlen": 16, "comment": "状态字", "index": "0x6041", "name": "status_word", "subindex": "0", "type": "uint16"}, {"bitlen": 16, "comment": "错误代码", "index": "0x603F", "name": "error_code", "subindex": "0", "type": "uint16"}, {"bitlen": 32, "comment": "实际反馈速度", "index": "0x606C", "name": "actual_velocity", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "厂商错误码", "index": "0x503F", "name": "customize_error_code", "subindex": "0", "type": "uint16"}], "vid": "0x00004321", "dc_config": {"assign_activate": "0x0300", "sync0_cycle": "", "sync0_shift": "", "sync1_cycle": "", "sync1_shift": ""}}, {"index": "22", "name": "L5N(COE)", "pid": "0x000000f0", "rx_pdo": "0x1600", "rx_pdos": [{"bitlen": 8, "comment": "操作模式设置", "index": "0x6060", "name": "operation_mode", "subindex": "0", "type": "uint8"}, {"bitlen": 32, "comment": "目标速度", "index": "0x60FF", "name": "target_speed", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "控制字", "index": "0x6040", "name": "control_word", "subindex": "0", "type": "uint16"}], "tx_pdo": "0x1a00", "tx_pdos": [{"bitlen": 16, "comment": "状态字", "index": "0x6041", "name": "status_word", "subindex": "0", "type": "uint16"}, {"bitlen": 16, "comment": "错误代码", "index": "0x603F", "name": "error_code", "subindex": "0", "type": "uint16"}, {"bitlen": 32, "comment": "实际反馈速度", "index": "0x606C", "name": "actual_velocity", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "厂商错误码", "index": "0x503F", "name": "customize_error_code", "subindex": "0", "type": "uint16"}], "vid": "0x00004321", "dc_config": {"assign_activate": "0x0300", "sync0_cycle": "", "sync0_shift": "", "sync1_cycle": "", "sync1_shift": ""}}, {"index": "23", "name": "L5N(COE)", "pid": "0x000000f0", "rx_pdo": "0x1600", "rx_pdos": [{"bitlen": 8, "comment": "操作模式设置", "index": "0x6060", "name": "operation_mode", "subindex": "0", "type": "uint8"}, {"bitlen": 32, "comment": "目标速度", "index": "0x60FF", "name": "target_speed", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "控制字", "index": "0x6040", "name": "control_word", "subindex": "0", "type": "uint16"}], "tx_pdo": "0x1a00", "tx_pdos": [{"bitlen": 16, "comment": "状态字", "index": "0x6041", "name": "status_word", "subindex": "0", "type": "uint16"}, {"bitlen": 16, "comment": "错误代码", "index": "0x603F", "name": "error_code", "subindex": "0", "type": "uint16"}, {"bitlen": 32, "comment": "实际反馈速度", "index": "0x606C", "name": "actual_velocity", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "厂商错误码", "index": "0x503F", "name": "customize_error_code", "subindex": "0", "type": "uint16"}], "vid": "0x00004321", "dc_config": {"assign_activate": "0x0300", "sync0_cycle": "", "sync0_shift": "", "sync1_cycle": "", "sync1_shift": ""}}, {"index": "24", "name": "L5N(COE)", "pid": "0x000000f0", "rx_pdo": "0x1600", "rx_pdos": [{"bitlen": 8, "comment": "操作模式设置", "index": "0x6060", "name": "operation_mode", "subindex": "0", "type": "uint8"}, {"bitlen": 32, "comment": "目标速度", "index": "0x60FF", "name": "target_speed", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "控制字", "index": "0x6040", "name": "control_word", "subindex": "0", "type": "uint16"}], "tx_pdo": "0x1a00", "tx_pdos": [{"bitlen": 16, "comment": "状态字", "index": "0x6041", "name": "status_word", "subindex": "0", "type": "uint16"}, {"bitlen": 16, "comment": "错误代码", "index": "0x603F", "name": "error_code", "subindex": "0", "type": "uint16"}, {"bitlen": 32, "comment": "实际反馈速度", "index": "0x606C", "name": "actual_velocity", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "厂商错误码", "index": "0x503F", "name": "customize_error_code", "subindex": "0", "type": "uint16"}], "vid": "0x00004321", "dc_config": {"assign_activate": "0x0300", "sync0_cycle": "", "sync0_shift": "", "sync1_cycle": "", "sync1_shift": ""}}, {"index": "25", "name": "L5N(COE)", "pid": "0x000000f0", "rx_pdo": "0x1600", "rx_pdos": [{"bitlen": 8, "comment": "操作模式设置", "index": "0x6060", "name": "operation_mode", "subindex": "0", "type": "uint8"}, {"bitlen": 32, "comment": "目标速度", "index": "0x60FF", "name": "target_speed", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "控制字", "index": "0x6040", "name": "control_word", "subindex": "0", "type": "uint16"}], "tx_pdo": "0x1a00", "tx_pdos": [{"bitlen": 16, "comment": "状态字", "index": "0x6041", "name": "status_word", "subindex": "0", "type": "uint16"}, {"bitlen": 16, "comment": "错误代码", "index": "0x603F", "name": "error_code", "subindex": "0", "type": "uint16"}, {"bitlen": 32, "comment": "实际反馈速度", "index": "0x606C", "name": "actual_velocity", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "厂商错误码", "index": "0x503F", "name": "customize_error_code", "subindex": "0", "type": "uint16"}], "vid": "0x00004321", "dc_config": {"assign_activate": "0x0300", "sync0_cycle": "", "sync0_shift": "", "sync1_cycle": "", "sync1_shift": ""}}, {"index": "26", "name": "L5N(COE)", "pid": "0x000000f0", "rx_pdo": "0x1600", "rx_pdos": [{"bitlen": 8, "comment": "操作模式设置", "index": "0x6060", "name": "operation_mode", "subindex": "0", "type": "uint8"}, {"bitlen": 32, "comment": "目标速度", "index": "0x60FF", "name": "target_speed", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "控制字", "index": "0x6040", "name": "control_word", "subindex": "0", "type": "uint16"}], "tx_pdo": "0x1a00", "tx_pdos": [{"bitlen": 16, "comment": "状态字", "index": "0x6041", "name": "status_word", "subindex": "0", "type": "uint16"}, {"bitlen": 16, "comment": "错误代码", "index": "0x603F", "name": "error_code", "subindex": "0", "type": "uint16"}, {"bitlen": 32, "comment": "实际反馈速度", "index": "0x606C", "name": "actual_velocity", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "厂商错误码", "index": "0x503F", "name": "customize_error_code", "subindex": "0", "type": "uint16"}], "vid": "0x00004321", "dc_config": {"assign_activate": "0x0300", "sync0_cycle": "", "sync0_shift": "", "sync1_cycle": "", "sync1_shift": ""}}, {"index": "27", "name": "L5N(COE)", "pid": "0x000000f0", "rx_pdo": "0x1600", "rx_pdos": [{"bitlen": 8, "comment": "操作模式设置", "index": "0x6060", "name": "operation_mode", "subindex": "0", "type": "uint8"}, {"bitlen": 32, "comment": "目标速度", "index": "0x60FF", "name": "target_speed", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "控制字", "index": "0x6040", "name": "control_word", "subindex": "0", "type": "uint16"}], "tx_pdo": "0x1a00", "tx_pdos": [{"bitlen": 16, "comment": "状态字", "index": "0x6041", "name": "status_word", "subindex": "0", "type": "uint16"}, {"bitlen": 16, "comment": "错误代码", "index": "0x603F", "name": "error_code", "subindex": "0", "type": "uint16"}, {"bitlen": 32, "comment": "实际反馈速度", "index": "0x606C", "name": "actual_velocity", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "厂商错误码", "index": "0x503F", "name": "customize_error_code", "subindex": "0", "type": "uint16"}], "vid": "0x00004321", "dc_config": {"assign_activate": "0x0300", "sync0_cycle": "", "sync0_shift": "", "sync1_cycle": "", "sync1_shift": ""}}, {"index": "28", "name": "L5N(COE)", "pid": "0x000000f0", "rx_pdo": "0x1600", "rx_pdos": [{"bitlen": 8, "comment": "操作模式设置", "index": "0x6060", "name": "operation_mode", "subindex": "0", "type": "uint8"}, {"bitlen": 32, "comment": "目标速度", "index": "0x60FF", "name": "target_speed", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "控制字", "index": "0x6040", "name": "control_word", "subindex": "0", "type": "uint16"}], "tx_pdo": "0x1a00", "tx_pdos": [{"bitlen": 16, "comment": "状态字", "index": "0x6041", "name": "status_word", "subindex": "0", "type": "uint16"}, {"bitlen": 16, "comment": "错误代码", "index": "0x603F", "name": "error_code", "subindex": "0", "type": "uint16"}, {"bitlen": 32, "comment": "实际反馈速度", "index": "0x606C", "name": "actual_velocity", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "厂商错误码", "index": "0x503F", "name": "customize_error_code", "subindex": "0", "type": "uint16"}], "vid": "0x00004321", "dc_config": {"assign_activate": "0x0300", "sync0_cycle": "", "sync0_shift": "", "sync1_cycle": "", "sync1_shift": ""}}, {"index": "29", "name": "L5N(COE)", "pid": "0x000000f0", "rx_pdo": "0x1600", "rx_pdos": [{"bitlen": 8, "comment": "操作模式设置", "index": "0x6060", "name": "operation_mode", "subindex": "0", "type": "uint8"}, {"bitlen": 32, "comment": "目标速度", "index": "0x60FF", "name": "target_speed", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "控制字", "index": "0x6040", "name": "control_word", "subindex": "0", "type": "uint16"}], "tx_pdo": "0x1a00", "tx_pdos": [{"bitlen": 16, "comment": "状态字", "index": "0x6041", "name": "status_word", "subindex": "0", "type": "uint16"}, {"bitlen": 16, "comment": "错误代码", "index": "0x603F", "name": "error_code", "subindex": "0", "type": "uint16"}, {"bitlen": 32, "comment": "实际反馈速度", "index": "0x606C", "name": "actual_velocity", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "厂商错误码", "index": "0x503F", "name": "customize_error_code", "subindex": "0", "type": "uint16"}], "vid": "0x00004321", "dc_config": {"assign_activate": "0x0300", "sync0_cycle": "", "sync0_shift": "", "sync1_cycle": "", "sync1_shift": ""}}, {"index": "30", "name": "L5N(COE)", "pid": "0x000000f0", "rx_pdo": "0x1600", "rx_pdos": [{"bitlen": 8, "comment": "操作模式设置", "index": "0x6060", "name": "operation_mode", "subindex": "0", "type": "uint8"}, {"bitlen": 32, "comment": "目标速度", "index": "0x60FF", "name": "target_speed", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "控制字", "index": "0x6040", "name": "control_word", "subindex": "0", "type": "uint16"}], "tx_pdo": "0x1a00", "tx_pdos": [{"bitlen": 16, "comment": "状态字", "index": "0x6041", "name": "status_word", "subindex": "0", "type": "uint16"}, {"bitlen": 16, "comment": "错误代码", "index": "0x603F", "name": "error_code", "subindex": "0", "type": "uint16"}, {"bitlen": 32, "comment": "实际反馈速度", "index": "0x606C", "name": "actual_velocity", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "厂商错误码", "index": "0x503F", "name": "customize_error_code", "subindex": "0", "type": "uint16"}], "vid": "0x00004321", "dc_config": {"assign_activate": "0x0300", "sync0_cycle": "", "sync0_shift": "", "sync1_cycle": "", "sync1_shift": ""}}, {"index": "31", "name": "L5N(COE)", "pid": "0x000000f0", "rx_pdo": "0x1600", "rx_pdos": [{"bitlen": 8, "comment": "操作模式设置", "index": "0x6060", "name": "operation_mode", "subindex": "0", "type": "uint8"}, {"bitlen": 32, "comment": "目标速度", "index": "0x60FF", "name": "target_speed", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "控制字", "index": "0x6040", "name": "control_word", "subindex": "0", "type": "uint16"}], "tx_pdo": "0x1a00", "tx_pdos": [{"bitlen": 16, "comment": "状态字", "index": "0x6041", "name": "status_word", "subindex": "0", "type": "uint16"}, {"bitlen": 16, "comment": "错误代码", "index": "0x603F", "name": "error_code", "subindex": "0", "type": "uint16"}, {"bitlen": 32, "comment": "实际反馈速度", "index": "0x606C", "name": "actual_velocity", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "厂商错误码", "index": "0x503F", "name": "customize_error_code", "subindex": "0", "type": "uint16"}], "vid": "0x00004321", "dc_config": {"assign_activate": "0x0300", "sync0_cycle": "", "sync0_shift": "", "sync1_cycle": "", "sync1_shift": ""}}, {"index": "32", "name": "L5N(COE)", "pid": "0x000000f0", "rx_pdo": "0x1600", "rx_pdos": [{"bitlen": 8, "comment": "操作模式设置", "index": "0x6060", "name": "operation_mode", "subindex": "0", "type": "uint8"}, {"bitlen": 32, "comment": "目标速度", "index": "0x60FF", "name": "target_speed", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "控制字", "index": "0x6040", "name": "control_word", "subindex": "0", "type": "uint16"}], "tx_pdo": "0x1a00", "tx_pdos": [{"bitlen": 16, "comment": "状态字", "index": "0x6041", "name": "status_word", "subindex": "0", "type": "uint16"}, {"bitlen": 16, "comment": "错误代码", "index": "0x603F", "name": "error_code", "subindex": "0", "type": "uint16"}, {"bitlen": 32, "comment": "实际反馈速度", "index": "0x606C", "name": "actual_velocity", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "厂商错误码", "index": "0x503F", "name": "customize_error_code", "subindex": "0", "type": "uint16"}], "vid": "0x00004321", "dc_config": {"assign_activate": "0x0300", "sync0_cycle": "", "sync0_shift": "", "sync1_cycle": "", "sync1_shift": ""}}, {"index": "33", "name": "L5N(COE)", "pid": "0x000000f0", "rx_pdo": "0x1600", "rx_pdos": [{"bitlen": 8, "comment": "操作模式设置", "index": "0x6060", "name": "operation_mode", "subindex": "0", "type": "uint8"}, {"bitlen": 32, "comment": "目标速度", "index": "0x60FF", "name": "target_speed", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "控制字", "index": "0x6040", "name": "control_word", "subindex": "0", "type": "uint16"}], "tx_pdo": "0x1a00", "tx_pdos": [{"bitlen": 16, "comment": "状态字", "index": "0x6041", "name": "status_word", "subindex": "0", "type": "uint16"}, {"bitlen": 16, "comment": "错误代码", "index": "0x603F", "name": "error_code", "subindex": "0", "type": "uint16"}, {"bitlen": 32, "comment": "实际反馈速度", "index": "0x606C", "name": "actual_velocity", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "厂商错误码", "index": "0x503F", "name": "customize_error_code", "subindex": "0", "type": "uint16"}], "vid": "0x00004321", "dc_config": {"assign_activate": "0x0300", "sync0_cycle": "", "sync0_shift": "", "sync1_cycle": "", "sync1_shift": ""}}, {"index": "34", "name": "L5N(COE)", "pid": "0x000000f0", "rx_pdo": "0x1600", "rx_pdos": [{"bitlen": 8, "comment": "操作模式设置", "index": "0x6060", "name": "operation_mode", "subindex": "0", "type": "uint8"}, {"bitlen": 32, "comment": "目标速度", "index": "0x60FF", "name": "target_speed", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "控制字", "index": "0x6040", "name": "control_word", "subindex": "0", "type": "uint16"}], "tx_pdo": "0x1a00", "tx_pdos": [{"bitlen": 16, "comment": "状态字", "index": "0x6041", "name": "status_word", "subindex": "0", "type": "uint16"}, {"bitlen": 16, "comment": "错误代码", "index": "0x603F", "name": "error_code", "subindex": "0", "type": "uint16"}, {"bitlen": 32, "comment": "实际反馈速度", "index": "0x606C", "name": "actual_velocity", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "厂商错误码", "index": "0x503F", "name": "customize_error_code", "subindex": "0", "type": "uint16"}], "vid": "0x00004321", "dc_config": {"assign_activate": "0x0300", "sync0_cycle": "", "sync0_shift": "", "sync1_cycle": "", "sync1_shift": ""}}, {"index": "35", "name": "L5N(COE)", "pid": "0x000000f0", "rx_pdo": "0x1600", "rx_pdos": [{"bitlen": 8, "comment": "操作模式设置", "index": "0x6060", "name": "operation_mode", "subindex": "0", "type": "uint8"}, {"bitlen": 32, "comment": "目标速度", "index": "0x60FF", "name": "target_speed", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "控制字", "index": "0x6040", "name": "control_word", "subindex": "0", "type": "uint16"}], "tx_pdo": "0x1a00", "tx_pdos": [{"bitlen": 16, "comment": "状态字", "index": "0x6041", "name": "status_word", "subindex": "0", "type": "uint16"}, {"bitlen": 16, "comment": "错误代码", "index": "0x603F", "name": "error_code", "subindex": "0", "type": "uint16"}, {"bitlen": 32, "comment": "实际反馈速度", "index": "0x606C", "name": "actual_velocity", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "厂商错误码", "index": "0x503F", "name": "customize_error_code", "subindex": "0", "type": "uint16"}], "vid": "0x00004321", "dc_config": {"assign_activate": "0x0300", "sync0_cycle": "", "sync0_shift": "", "sync1_cycle": "", "sync1_shift": ""}}, {"index": "36", "name": "L5N(COE)", "pid": "0x000000f0", "rx_pdo": "0x1600", "rx_pdos": [{"bitlen": 8, "comment": "操作模式设置", "index": "0x6060", "name": "operation_mode", "subindex": "0", "type": "uint8"}, {"bitlen": 32, "comment": "目标速度", "index": "0x60FF", "name": "target_speed", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "控制字", "index": "0x6040", "name": "control_word", "subindex": "0", "type": "uint16"}], "tx_pdo": "0x1a00", "tx_pdos": [{"bitlen": 16, "comment": "状态字", "index": "0x6041", "name": "status_word", "subindex": "0", "type": "uint16"}, {"bitlen": 16, "comment": "错误代码", "index": "0x603F", "name": "error_code", "subindex": "0", "type": "uint16"}, {"bitlen": 32, "comment": "实际反馈速度", "index": "0x606C", "name": "actual_velocity", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "厂商错误码", "index": "0x503F", "name": "customize_error_code", "subindex": "0", "type": "uint16"}], "vid": "0x00004321", "dc_config": {"assign_activate": "0x0300", "sync0_cycle": "", "sync0_shift": "", "sync1_cycle": "", "sync1_shift": ""}}, {"index": "37", "name": "L5N(COE)", "pid": "0x000000f0", "rx_pdo": "0x1600", "rx_pdos": [{"bitlen": 8, "comment": "操作模式设置", "index": "0x6060", "name": "operation_mode", "subindex": "0", "type": "uint8"}, {"bitlen": 32, "comment": "目标速度", "index": "0x60FF", "name": "target_speed", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "控制字", "index": "0x6040", "name": "control_word", "subindex": "0", "type": "uint16"}], "tx_pdo": "0x1a00", "tx_pdos": [{"bitlen": 16, "comment": "状态字", "index": "0x6041", "name": "status_word", "subindex": "0", "type": "uint16"}, {"bitlen": 16, "comment": "错误代码", "index": "0x603F", "name": "error_code", "subindex": "0", "type": "uint16"}, {"bitlen": 32, "comment": "实际反馈速度", "index": "0x606C", "name": "actual_velocity", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "厂商错误码", "index": "0x503F", "name": "customize_error_code", "subindex": "0", "type": "uint16"}], "vid": "0x00004321", "dc_config": {"assign_activate": "0x0300", "sync0_cycle": "", "sync0_shift": "", "sync1_cycle": "", "sync1_shift": ""}}, {"index": "38", "name": "L5N(COE)", "pid": "0x000000f0", "rx_pdo": "0x1600", "rx_pdos": [{"bitlen": 8, "comment": "操作模式设置", "index": "0x6060", "name": "operation_mode", "subindex": "0", "type": "uint8"}, {"bitlen": 32, "comment": "目标速度", "index": "0x60FF", "name": "target_speed", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "控制字", "index": "0x6040", "name": "control_word", "subindex": "0", "type": "uint16"}], "tx_pdo": "0x1a00", "tx_pdos": [{"bitlen": 16, "comment": "状态字", "index": "0x6041", "name": "status_word", "subindex": "0", "type": "uint16"}, {"bitlen": 16, "comment": "错误代码", "index": "0x603F", "name": "error_code", "subindex": "0", "type": "uint16"}, {"bitlen": 32, "comment": "实际反馈速度", "index": "0x606C", "name": "actual_velocity", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "厂商错误码", "index": "0x503F", "name": "customize_error_code", "subindex": "0", "type": "uint16"}], "vid": "0x00004321", "dc_config": {"assign_activate": "0x0300", "sync0_cycle": "", "sync0_shift": "", "sync1_cycle": "", "sync1_shift": ""}}, {"index": "39", "name": "L5N(COE)", "pid": "0x000000f0", "rx_pdo": "0x1600", "rx_pdos": [{"bitlen": 8, "comment": "操作模式设置", "index": "0x6060", "name": "operation_mode", "subindex": "0", "type": "uint8"}, {"bitlen": 32, "comment": "目标速度", "index": "0x60FF", "name": "target_speed", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "控制字", "index": "0x6040", "name": "control_word", "subindex": "0", "type": "uint16"}], "tx_pdo": "0x1a00", "tx_pdos": [{"bitlen": 16, "comment": "状态字", "index": "0x6041", "name": "status_word", "subindex": "0", "type": "uint16"}, {"bitlen": 16, "comment": "错误代码", "index": "0x603F", "name": "error_code", "subindex": "0", "type": "uint16"}, {"bitlen": 32, "comment": "实际反馈速度", "index": "0x606C", "name": "actual_velocity", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "厂商错误码", "index": "0x503F", "name": "customize_error_code", "subindex": "0", "type": "uint16"}], "vid": "0x00004321", "dc_config": {"assign_activate": "0x0300", "sync0_cycle": "", "sync0_shift": "", "sync1_cycle": "", "sync1_shift": ""}}, {"index": "40", "name": "L5N(COE)", "pid": "0x000000f0", "rx_pdo": "0x1600", "rx_pdos": [{"bitlen": 8, "comment": "操作模式设置", "index": "0x6060", "name": "operation_mode", "subindex": "0", "type": "uint8"}, {"bitlen": 32, "comment": "目标速度", "index": "0x60FF", "name": "target_speed", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "控制字", "index": "0x6040", "name": "control_word", "subindex": "0", "type": "uint16"}], "tx_pdo": "0x1a00", "tx_pdos": [{"bitlen": 16, "comment": "状态字", "index": "0x6041", "name": "status_word", "subindex": "0", "type": "uint16"}, {"bitlen": 16, "comment": "错误代码", "index": "0x603F", "name": "error_code", "subindex": "0", "type": "uint16"}, {"bitlen": 32, "comment": "实际反馈速度", "index": "0x606C", "name": "actual_velocity", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "厂商错误码", "index": "0x503F", "name": "customize_error_code", "subindex": "0", "type": "uint16"}], "vid": "0x00004321", "dc_config": {"assign_activate": "0x0300", "sync0_cycle": "", "sync0_shift": "", "sync1_cycle": "", "sync1_shift": ""}}, {"index": "41", "name": "L5N(COE)", "pid": "0x000000f0", "rx_pdo": "0x1600", "rx_pdos": [{"bitlen": 8, "comment": "操作模式设置", "index": "0x6060", "name": "operation_mode", "subindex": "0", "type": "uint8"}, {"bitlen": 32, "comment": "目标速度", "index": "0x60FF", "name": "target_speed", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "控制字", "index": "0x6040", "name": "control_word", "subindex": "0", "type": "uint16"}], "tx_pdo": "0x1a00", "tx_pdos": [{"bitlen": 16, "comment": "状态字", "index": "0x6041", "name": "status_word", "subindex": "0", "type": "uint16"}, {"bitlen": 16, "comment": "错误代码", "index": "0x603F", "name": "error_code", "subindex": "0", "type": "uint16"}, {"bitlen": 32, "comment": "实际反馈速度", "index": "0x606C", "name": "actual_velocity", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "厂商错误码", "index": "0x503F", "name": "customize_error_code", "subindex": "0", "type": "uint16"}], "vid": "0x00004321", "dc_config": {"assign_activate": "0x0300", "sync0_cycle": "", "sync0_shift": "", "sync1_cycle": "", "sync1_shift": ""}}, {"index": "42", "name": "L5N(COE)", "pid": "0x000000f0", "rx_pdo": "0x1600", "rx_pdos": [{"bitlen": 8, "comment": "操作模式设置", "index": "0x6060", "name": "operation_mode", "subindex": "0", "type": "uint8"}, {"bitlen": 32, "comment": "目标速度", "index": "0x60FF", "name": "target_speed", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "控制字", "index": "0x6040", "name": "control_word", "subindex": "0", "type": "uint16"}], "tx_pdo": "0x1a00", "tx_pdos": [{"bitlen": 16, "comment": "状态字", "index": "0x6041", "name": "status_word", "subindex": "0", "type": "uint16"}, {"bitlen": 16, "comment": "错误代码", "index": "0x603F", "name": "error_code", "subindex": "0", "type": "uint16"}, {"bitlen": 32, "comment": "实际反馈速度", "index": "0x606C", "name": "actual_velocity", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "厂商错误码", "index": "0x503F", "name": "customize_error_code", "subindex": "0", "type": "uint16"}], "vid": "0x00004321", "dc_config": {"assign_activate": "0x0300", "sync0_cycle": "", "sync0_shift": "", "sync1_cycle": "", "sync1_shift": ""}}, {"index": "43", "name": "L5N(COE)", "pid": "0x000000f0", "rx_pdo": "0x1600", "rx_pdos": [{"bitlen": 8, "comment": "操作模式设置", "index": "0x6060", "name": "operation_mode", "subindex": "0", "type": "uint8"}, {"bitlen": 32, "comment": "目标速度", "index": "0x60FF", "name": "target_speed", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "控制字", "index": "0x6040", "name": "control_word", "subindex": "0", "type": "uint16"}], "tx_pdo": "0x1a00", "tx_pdos": [{"bitlen": 16, "comment": "状态字", "index": "0x6041", "name": "status_word", "subindex": "0", "type": "uint16"}, {"bitlen": 16, "comment": "错误代码", "index": "0x603F", "name": "error_code", "subindex": "0", "type": "uint16"}, {"bitlen": 32, "comment": "实际反馈速度", "index": "0x606C", "name": "actual_velocity", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "厂商错误码", "index": "0x503F", "name": "customize_error_code", "subindex": "0", "type": "uint16"}], "vid": "0x00004321", "dc_config": {"assign_activate": "0x0300", "sync0_cycle": "", "sync0_shift": "", "sync1_cycle": "", "sync1_shift": ""}}, {"index": "44", "name": "L5N(COE)", "pid": "0x000000f0", "rx_pdo": "0x1600", "rx_pdos": [{"bitlen": 8, "comment": "操作模式设置", "index": "0x6060", "name": "operation_mode", "subindex": "0", "type": "uint8"}, {"bitlen": 32, "comment": "目标速度", "index": "0x60FF", "name": "target_speed", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "控制字", "index": "0x6040", "name": "control_word", "subindex": "0", "type": "uint16"}], "tx_pdo": "0x1a00", "tx_pdos": [{"bitlen": 16, "comment": "状态字", "index": "0x6041", "name": "status_word", "subindex": "0", "type": "uint16"}, {"bitlen": 16, "comment": "错误代码", "index": "0x603F", "name": "error_code", "subindex": "0", "type": "uint16"}, {"bitlen": 32, "comment": "实际反馈速度", "index": "0x606C", "name": "actual_velocity", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "厂商错误码", "index": "0x503F", "name": "customize_error_code", "subindex": "0", "type": "uint16"}], "vid": "0x00004321", "dc_config": {"assign_activate": "0x0300", "sync0_cycle": "", "sync0_shift": "", "sync1_cycle": "", "sync1_shift": ""}}, {"index": "45", "name": "L5N(COE)", "pid": "0x000000f0", "rx_pdo": "0x1600", "rx_pdos": [{"bitlen": 8, "comment": "操作模式设置", "index": "0x6060", "name": "operation_mode", "subindex": "0", "type": "uint8"}, {"bitlen": 32, "comment": "目标速度", "index": "0x60FF", "name": "target_speed", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "控制字", "index": "0x6040", "name": "control_word", "subindex": "0", "type": "uint16"}], "tx_pdo": "0x1a00", "tx_pdos": [{"bitlen": 16, "comment": "状态字", "index": "0x6041", "name": "status_word", "subindex": "0", "type": "uint16"}, {"bitlen": 16, "comment": "错误代码", "index": "0x603F", "name": "error_code", "subindex": "0", "type": "uint16"}, {"bitlen": 32, "comment": "实际反馈速度", "index": "0x606C", "name": "actual_velocity", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "厂商错误码", "index": "0x503F", "name": "customize_error_code", "subindex": "0", "type": "uint16"}], "vid": "0x00004321", "dc_config": {"assign_activate": "0x0300", "sync0_cycle": "", "sync0_shift": "", "sync1_cycle": "", "sync1_shift": ""}}, {"index": "46", "name": "L5N(COE)", "pid": "0x000000f0", "rx_pdo": "0x1600", "rx_pdos": [{"bitlen": 8, "comment": "操作模式设置", "index": "0x6060", "name": "operation_mode", "subindex": "0", "type": "uint8"}, {"bitlen": 32, "comment": "目标速度", "index": "0x60FF", "name": "target_speed", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "控制字", "index": "0x6040", "name": "control_word", "subindex": "0", "type": "uint16"}], "tx_pdo": "0x1a00", "tx_pdos": [{"bitlen": 16, "comment": "状态字", "index": "0x6041", "name": "status_word", "subindex": "0", "type": "uint16"}, {"bitlen": 16, "comment": "错误代码", "index": "0x603F", "name": "error_code", "subindex": "0", "type": "uint16"}, {"bitlen": 32, "comment": "实际反馈速度", "index": "0x606C", "name": "actual_velocity", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "厂商错误码", "index": "0x503F", "name": "customize_error_code", "subindex": "0", "type": "uint16"}], "vid": "0x00004321", "dc_config": {"assign_activate": "0x0300", "sync0_cycle": "", "sync0_shift": "", "sync1_cycle": "", "sync1_shift": ""}}, {"index": "47", "name": "L5N(COE)", "pid": "0x000000f0", "rx_pdo": "0x1600", "rx_pdos": [{"bitlen": 8, "comment": "操作模式设置", "index": "0x6060", "name": "operation_mode", "subindex": "0", "type": "uint8"}, {"bitlen": 32, "comment": "目标速度", "index": "0x60FF", "name": "target_speed", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "控制字", "index": "0x6040", "name": "control_word", "subindex": "0", "type": "uint16"}], "tx_pdo": "0x1a00", "tx_pdos": [{"bitlen": 16, "comment": "状态字", "index": "0x6041", "name": "status_word", "subindex": "0", "type": "uint16"}, {"bitlen": 16, "comment": "错误代码", "index": "0x603F", "name": "error_code", "subindex": "0", "type": "uint16"}, {"bitlen": 32, "comment": "实际反馈速度", "index": "0x606C", "name": "actual_velocity", "subindex": "0", "type": "int32"}, {"bitlen": 16, "comment": "厂商错误码", "index": "0x503F", "name": "customize_error_code", "subindex": "0", "type": "uint16"}], "vid": "0x00004321", "dc_config": {"assign_activate": "0x0300", "sync0_cycle": "", "sync0_shift": "", "sync1_cycle": "", "sync1_shift": ""}}]}