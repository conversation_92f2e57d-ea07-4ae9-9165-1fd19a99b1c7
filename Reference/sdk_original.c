#define _GNU_SOURCE
#include "sdk.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <pthread.h>
#include <unistd.h> // For sleep, usleep
#include <sys/mman.h>
#include <fcntl.h>
#include <sched.h>
#include <sys/stat.h>
#include <time.h>   // For clock_gettime, CLOCK_MONOTONIC, etc.
#include <errno.h>  // For errno

#include "ecrt.h" // EtherCAT master library

// --- Internal SDK Configuration and State ---

// Shared memory configuration (eventually from JSON)
#define INTERNAL_SHM_FILE "qb3tau3h4rlo2tc_L5N_Test_shm_sdk" // Default, can be overridden by JSON
#define TASK_FREQUENCY 4000 // Hz (eventually from JSON)
#define NSEC_PER_SEC (1000000000L)
#define PERIOD_NS (NSEC_PER_SEC / TASK_FREQUENCY)
#define STATUS_UPDATE_INTERVAL_MS 1000
#define STATUS_UPDATE_PERIOD ((TASK_FREQUENCY * STATUS_UPDATE_INTERVAL_MS) / 1000)
#define TIMESPEC2NS(T) ((uint64_t) (T).tv_sec * NSEC_PER_SEC + (T).tv_nsec)

// PDO/DC configuration thread parameters (copied from L5N_Test_middleware)
typedef struct {
    ec_slave_config_t *slave_config;
    ec_sync_info_t *sync_info;
    int *result;
} config_thread_param_t;

typedef struct {
    ec_slave_config_t *slave_config;
    uint16_t assign_activate;
    uint32_t sync0_cycle;
    uint32_t sync0_shift;
    uint32_t sync1_cycle;
    uint32_t sync1_shift;
    int *result;
} dc_config_thread_param_t;


// Internal representation of shared memory data
// Matches L5N_Test_middleware, but uses fixed-width types.
// This structure definition would ideally be generated from JSON.
typedef struct {
    int32_t shm_slave0_online_status;
    int32_t shm_slave0_operational_status;
    int32_t shm_slave0_al_state;
    int32_t shm_slave0_rx_0x6060_operation_mode;
    int32_t shm_slave0_rx_0x60ff_target_speed;
    int32_t shm_slave0_rx_0x6040_control_word;
    int32_t shm_slave0_tx_0x6041_status_word;
    int32_t shm_slave0_tx_0x603f_error_code;
    int32_t shm_slave0_tx_0x606c_actual_velocity;
    int32_t shm_slave0_tx_0x503f_customize_error_code;
} sdk_internal_shm_data_t;

static sdk_internal_shm_data_t *g_sdk_shm_ptr = NULL;
static size_t g_sdk_shm_size = sizeof(sdk_internal_shm_data_t);
static char g_shm_name[256] = INTERNAL_SHM_FILE; // Can be overridden by JSON via sdk_init

// EtherCAT master and domain data
static ec_master_t *g_master = NULL;
static ec_master_state_t g_master_state = {};
static ec_domain_t *g_domain1 = NULL;
static ec_domain_state_t g_domain1_state = {};
static uint8_t *g_domain1_pd = NULL; // Process data

// Slave configuration data (for slave 0, as in L5N_Test_middleware)
// This would be more dynamic with JSON configuration for multiple slaves.
static ec_slave_config_t *g_sc_slave0 = NULL;
static ec_slave_config_state_t g_sc_slave0_state = {};

#define SLAVE0_POS 0,0
#define SLAVE0_VID_PID 0x00004321,0x000000f0 // Example, from L5N_Test_middleware

// PDO entry offsets (mirrors structure in L5N_Test_middleware)
// These would be dynamically determined based on JSON in a full implementation.
static struct {
    unsigned int pdo_slave0_rx_0x6060_operation_mode;
    unsigned int pdo_slave0_rx_0x60ff_target_speed;
    unsigned int pdo_slave0_rx_0x6040_control_word;
    unsigned int pdo_slave0_tx_0x6041_status_word;
    unsigned int pdo_slave0_tx_0x603f_error_code;
    unsigned int pdo_slave0_tx_0x606c_actual_velocity;
    unsigned int pdo_slave0_tx_0x503f_customize_error_code;
} g_pdo_offsets;

// PDO registration list (mirrors L5N_Test_middleware for slave 0)
// This would be dynamically generated.
const static ec_pdo_entry_reg_t domain1_regs[] = {
    {SLAVE0_POS, SLAVE0_VID_PID, 0x6060, 0, &g_pdo_offsets.pdo_slave0_rx_0x6060_operation_mode},
    {SLAVE0_POS, SLAVE0_VID_PID, 0x60ff, 0, &g_pdo_offsets.pdo_slave0_rx_0x60ff_target_speed},
    {SLAVE0_POS, SLAVE0_VID_PID, 0x6040, 0, &g_pdo_offsets.pdo_slave0_rx_0x6040_control_word},
    {SLAVE0_POS, SLAVE0_VID_PID, 0x6041, 0, &g_pdo_offsets.pdo_slave0_tx_0x6041_status_word},
    {SLAVE0_POS, SLAVE0_VID_PID, 0x603F, 0, &g_pdo_offsets.pdo_slave0_tx_0x603f_error_code},
    {SLAVE0_POS, SLAVE0_VID_PID, 0x606C, 0, &g_pdo_offsets.pdo_slave0_tx_0x606c_actual_velocity},
    {SLAVE0_POS, SLAVE0_VID_PID, 0x503F, 0, &g_pdo_offsets.pdo_slave0_tx_0x503f_customize_error_code},
    {}
};

// PDO and Sync manager configurations for slave 0 (mirrors L5N_Test_middleware)
// These would also be dynamically generated.
static ec_pdo_entry_info_t slave0_pdo_entries[] = {
    {0x6060, 0, 8},  /* operation_mode */
    {0x60ff, 0, 32}, /* target_speed */
    {0x6040, 0, 16}, /* control_word */
    {0x6041, 0, 16}, /* status_word */
    {0x603F, 0, 16}, /* error_code */
    {0x606C, 0, 32}, /* actual_velocity */
    {0x503F, 0, 16}, /* customize_error_code */
};

static ec_pdo_info_t slave0_pdos[] = {
    {0x1600, 3, slave0_pdo_entries + 0}, /* RxPDO */
    {0x1a00, 4, slave0_pdo_entries + 3}, /* TxPDO */
};

static ec_sync_info_t slave0_syncs[] = {
    {0, EC_DIR_OUTPUT, 0, NULL, EC_WD_DISABLE},
    {1, EC_DIR_INPUT, 0, NULL, EC_WD_DISABLE},
    {2, EC_DIR_OUTPUT, 1, slave0_pdos + 0, EC_WD_ENABLE},
    {3, EC_DIR_INPUT, 1, slave0_pdos + 1, EC_WD_DISABLE},
    {0xff}
};

// Cyclic task control
static volatile int g_run_cyclic_task = 0;
static pthread_t g_cyclic_task_thread_id;
static pthread_mutex_t g_shm_mutex = PTHREAD_MUTEX_INITIALIZER;
static int g_core_affinity = -1; // -1 means no affinity set

// --- Helper Functions (mostly from L5N_Test_middleware) ---

// PDO configuration thread function (copied from L5N_Test_middleware)
static void* config_slave_thread_func(void *arg) {
    config_thread_param_t *param = (config_thread_param_t *)arg;
    *(param->result) = ecrt_slave_config_pdos(param->slave_config, EC_END, param->sync_info);
    return NULL;
}

// DC configuration thread function (copied from L5N_Test_middleware)
static void* dc_config_thread_func(void *arg) {
    dc_config_thread_param_t *param = (dc_config_thread_param_t *)arg;
    int ret = ecrt_slave_config_dc(
        param->slave_config,
        param->assign_activate,
        param->sync0_cycle,
        param->sync0_shift,
        param->sync1_cycle,
        param->sync1_shift
    );
    *(param->result) = ret;
    return NULL;
}


static void internal_create_shm() {
    // TODO: Parse SHM name from JSON in a real scenario
    // For now, g_shm_name is used (default or overridden by sdk_init if implemented)

    int fd = shm_open(g_shm_name, O_CREAT | O_RDWR, 0666);
    if (fd < 0) {
        perror("SDK: shm_open failed");
        // sdk_init will handle cleanup if this fails
        g_sdk_shm_ptr = MAP_FAILED; 
        return;
    }

    if (ftruncate(fd, g_sdk_shm_size) < 0) {
        perror("SDK: ftruncate failed");
        close(fd);
        shm_unlink(g_shm_name); // Clean up on ftruncate failure
        g_sdk_shm_ptr = MAP_FAILED;
        return;
    }

    g_sdk_shm_ptr = (sdk_internal_shm_data_t *)mmap(NULL, g_sdk_shm_size,
                                                 PROT_READ | PROT_WRITE,
                                                 MAP_SHARED, fd, 0);
    if (g_sdk_shm_ptr == MAP_FAILED) {
        perror("SDK: mmap failed");
        close(fd);
        shm_unlink(g_shm_name); // Clean up on mmap failure
        return;
    }
    close(fd); // Descriptor can be closed after mmap
    memset(g_sdk_shm_ptr, 0, g_sdk_shm_size);
    fprintf(stdout, "SDK: Shared memory '%s' created/opened successfully.\n", g_shm_name);
}

static void internal_cleanup_shm() {
    if (g_sdk_shm_ptr != NULL && g_sdk_shm_ptr != MAP_FAILED) {
        if (munmap(g_sdk_shm_ptr, g_sdk_shm_size) == -1) {
            perror("SDK: munmap failed");
        }
        g_sdk_shm_ptr = NULL; 
        // Unlink only if this process created it and is responsible for it.
        // The middleware host might decide this. For a self-contained SDK, it should unlink.
        if (shm_unlink(g_shm_name) == -1) {
             // ENOENT is fine if it was already unlinked or never created properly
            if (errno != ENOENT) {
                perror("SDK: shm_unlink failed");
            }
        }
        fprintf(stdout, "SDK: Shared memory '%s' unmapped and unlinked.\n", g_shm_name);
    } else {
        // Ensure unlink is attempted if mapping failed but shm_open might have succeeded
        shm_unlink(g_shm_name);
    }
}

static void timespec_add_ns(struct timespec *ts, uint64_t ns) {
    ts->tv_nsec += ns;
    while (ts->tv_nsec >= NSEC_PER_SEC) {
        ts->tv_nsec -= NSEC_PER_SEC;
        ts->tv_sec++;
    }
}

static void* cyclic_task_main(void* arg) {
    (void)arg; // Unused
    struct timespec wakeup_time;
    int cycle_counter = 0;

    // Set CPU affinity if requested
    if (g_core_affinity != -1) {
        cpu_set_t cpuset;
        CPU_ZERO(&cpuset);
        CPU_SET(g_core_affinity, &cpuset);
        if (pthread_setaffinity_np(pthread_self(), sizeof(cpu_set_t), &cpuset) != 0) {
            perror("SDK: pthread_setaffinity_np failed");
            // Continue without affinity
        } else {
            fprintf(stdout, "SDK: Cyclic task pinned to CPU core %d\n", g_core_affinity);
        }
    }
    
    // Set real-time priority for this thread
    struct sched_param sparam;
    sparam.sched_priority = sched_get_priority_max(SCHED_FIFO);
    if (sched_setscheduler(0, SCHED_FIFO, &sparam) == -1) {
        perror("SDK: sched_setscheduler for cyclic task failed (run with sudo?)");
        // Continue with default scheduling
    } else {
         fprintf(stdout, "SDK: Cyclic task using SCHED_FIFO priority %d.\n", sparam.sched_priority);
    }


    clock_gettime(CLOCK_MONOTONIC, &wakeup_time);

    while (g_run_cyclic_task) {
        timespec_add_ns(&wakeup_time, PERIOD_NS);
        clock_nanosleep(CLOCK_MONOTONIC, TIMER_ABSTIME, &wakeup_time, NULL);

        ecrt_master_application_time(g_master, TIMESPEC2NS(wakeup_time));
        ecrt_master_receive(g_master);
        
        if (ecrt_master_state(g_master, &g_master_state)) {
             fprintf(stderr, "SDK: Failed to get master state in cyclic task.\n");
             g_run_cyclic_task = 0; // Stop on error
             break;
        }

        ecrt_domain_process(g_domain1);

        pthread_mutex_lock(&g_shm_mutex);

        if (cycle_counter > 0) {
            cycle_counter--;
        } else {
            cycle_counter = STATUS_UPDATE_PERIOD;
            ecrt_slave_config_state(g_sc_slave0, &g_sc_slave0_state);
            if (g_sdk_shm_ptr) { // Check if SHM is valid
                g_sdk_shm_ptr->shm_slave0_online_status = g_sc_slave0_state.online;
                g_sdk_shm_ptr->shm_slave0_operational_status = g_sc_slave0_state.operational;
                g_sdk_shm_ptr->shm_slave0_al_state = g_sc_slave0_state.al_state;
            }
        }

        if (g_domain1_pd && g_sdk_shm_ptr) { // Check if PD and SHM are valid
            // Update shared memory with TxPDO data (from EtherCAT slave to SHM)
            g_sdk_shm_ptr->shm_slave0_tx_0x6041_status_word = EC_READ_U16(g_domain1_pd + g_pdo_offsets.pdo_slave0_tx_0x6041_status_word);
            g_sdk_shm_ptr->shm_slave0_tx_0x603f_error_code = EC_READ_U16(g_domain1_pd + g_pdo_offsets.pdo_slave0_tx_0x603f_error_code);
            g_sdk_shm_ptr->shm_slave0_tx_0x606c_actual_velocity = EC_READ_S32(g_domain1_pd + g_pdo_offsets.pdo_slave0_tx_0x606c_actual_velocity);
            g_sdk_shm_ptr->shm_slave0_tx_0x503f_customize_error_code = EC_READ_U16(g_domain1_pd + g_pdo_offsets.pdo_slave0_tx_0x503f_customize_error_code);

            // Write RxPDO data from SHM to EtherCAT domain (from SHM to EtherCAT slave)
            EC_WRITE_U8(g_domain1_pd + g_pdo_offsets.pdo_slave0_rx_0x6060_operation_mode, (uint8_t)g_sdk_shm_ptr->shm_slave0_rx_0x6060_operation_mode);
            EC_WRITE_S32(g_domain1_pd + g_pdo_offsets.pdo_slave0_rx_0x60ff_target_speed, g_sdk_shm_ptr->shm_slave0_rx_0x60ff_target_speed);
            EC_WRITE_U16(g_domain1_pd + g_pdo_offsets.pdo_slave0_rx_0x6040_control_word, (uint16_t)g_sdk_shm_ptr->shm_slave0_rx_0x6040_control_word);
        }
        
        pthread_mutex_unlock(&g_shm_mutex);

        ecrt_domain_queue(g_domain1);
        ecrt_master_sync_slave_clocks(g_master); // For DC
        ecrt_master_sync_reference_clock(g_master); // For DC
        ecrt_master_send(g_master);
    }
    fprintf(stdout, "SDK: Cyclic task finished.\n");
    return NULL;
}


// --- Public API Implementation ---

__attribute__((visibility("default"))) int sdk_init() {
    fprintf(stdout, "SDK: Initializing... (Note: Using hardcoded internal configuration)\n");
    
    // TODO: Parse JSON config to override g_shm_name, TASK_FREQUENCY, slave configs, PDOs etc.
    // For now, using hardcoded values.

    if (mlockall(MCL_CURRENT | MCL_FUTURE) == -1) {
        perror("SDK: mlockall failed");
        // Non-fatal for SDK lib, but host should be aware.
    }

    internal_create_shm();
    if (g_sdk_shm_ptr == MAP_FAILED || g_sdk_shm_ptr == NULL) {
        fprintf(stderr, "SDK: Failed to create or map shared memory.\n");
        return -1; // SHM is critical
    }

    g_master = ecrt_request_master(0); // Master index 0
    if (!g_master) {
        fprintf(stderr, "SDK: Failed to request master.\n");
        internal_cleanup_shm();
        return -2;
    }

    g_domain1 = ecrt_master_create_domain(g_master);
    if (!g_domain1) {
        fprintf(stderr, "SDK: Failed to create domain.\n");
        ecrt_release_master(g_master);
        g_master = NULL;
        internal_cleanup_shm();
        return -3;
    }

    // Configure slave 0 (example, replace with dynamic config from JSON)
    g_sc_slave0 = ecrt_master_slave_config(g_master, SLAVE0_POS, SLAVE0_VID_PID);
    if (!g_sc_slave0) {
        fprintf(stderr, "SDK: Failed to get slave 0 configuration.\n");
        // ecrt_master_create_domain does not need explicit cleanup here, master release handles it
        ecrt_release_master(g_master);
        g_master = NULL;
        internal_cleanup_shm();
        return -4;
    }

    // Configure PDOs for slave 0 (using pthreads like in L5N_Test_middleware)
    // This section would be generalized for all slaves based on JSON.
    pthread_t pdo_config_thread;
    int pdo_config_result = 0;
    config_thread_param_t pdo_param;
    pdo_param.slave_config = g_sc_slave0;
    pdo_param.sync_info = slave0_syncs; // Hardcoded for slave0
    pdo_param.result = &pdo_config_result;

    if (pthread_create(&pdo_config_thread, NULL, config_slave_thread_func, &pdo_param)) {
        fprintf(stderr, "SDK: Failed to create PDO config thread for slave 0.\n");
        // No easy way to cleanup ec_slave_config_t directly
        ecrt_release_master(g_master);
        g_master = NULL;
        internal_cleanup_shm();
        return -5;
    }
    pthread_join(pdo_config_thread, NULL);
    if (pdo_config_result != 0) {
        fprintf(stderr, "SDK: Failed to configure PDOs for slave 0 (result: %d).\n", pdo_config_result);
        ecrt_release_master(g_master);
        g_master = NULL;
        internal_cleanup_shm();
        return -6;
    }
    fprintf(stdout, "SDK: PDOs configured for slave 0.\n");

    // Configure DC for slave 0 (example)
    pthread_t dc_config_thread;
    int dc_config_result = 0;
    dc_config_thread_param_t dc_param;
    dc_param.slave_config = g_sc_slave0;
    dc_param.assign_activate = 0x0300; // Activate SYNC0
    dc_param.sync0_cycle = PERIOD_NS;
    dc_param.sync0_shift = PERIOD_NS / 3; // Example shift
    dc_param.sync1_cycle = 0;
    dc_param.sync1_shift = 0;
    dc_param.result = &dc_config_result;

    if (pthread_create(&dc_config_thread, NULL, dc_config_thread_func, &dc_param)) {
        fprintf(stderr, "SDK: Failed to create DC config thread for slave 0.\n");
        ecrt_release_master(g_master);
        g_master = NULL;
        internal_cleanup_shm();
        return -7;
    }
    pthread_join(dc_config_thread, NULL);
    if (dc_config_result != 0) {
        fprintf(stderr, "SDK: Failed to configure DC for slave 0 (result: %d).\n", dc_config_result);
        ecrt_release_master(g_master);
        g_master = NULL;
        internal_cleanup_shm();
        return -8;
    }
     fprintf(stdout, "SDK: DC configured for slave 0.\n");


    if (ecrt_domain_reg_pdo_entry_list(g_domain1, domain1_regs)) {
        fprintf(stderr, "SDK: PDO entry registration failed!\n");
        ecrt_release_master(g_master);
        g_master = NULL;
        internal_cleanup_shm();
        return -9;
    }
    fprintf(stdout, "SDK: PDO entries registered to domain.\n");

    if (ecrt_master_activate(g_master)) {
        fprintf(stderr, "SDK: Master activation failed!\n");
        ecrt_release_master(g_master);
        g_master = NULL;
        internal_cleanup_shm();
        return -10;
    }
    fprintf(stdout, "SDK: Master activated.\n");

    g_domain1_pd = ecrt_domain_data(g_domain1);
    if (!g_domain1_pd) {
        fprintf(stderr, "SDK: Failed to get domain data pointer!\n");
        // No explicit deactivation needed if data pointer fails after activation?
        // ecrt_master_deactivate(g_master); // Consider this
        ecrt_release_master(g_master);
        g_master = NULL;
        internal_cleanup_shm();
        return -11;
    }
    fprintf(stdout, "SDK: Domain data pointer obtained.\n");
    fprintf(stdout, "SDK: Initialization successful.\n");
    return 0; // Success
}

__attribute__((visibility("default"))) int sdk_start_cyclic_task() {
    if (!g_master || !g_domain1_pd) {
        fprintf(stderr, "SDK: Cannot start cyclic task, SDK not initialized or master not active.\n");
        return -1;
    }
    if (g_run_cyclic_task) {
        fprintf(stderr, "SDK: Cyclic task already running.\n");
        return -2;
    }

    g_run_cyclic_task = 1;
    if (pthread_create(&g_cyclic_task_thread_id, NULL, cyclic_task_main, NULL)) {
        perror("SDK: Failed to create cyclic task thread");
        g_run_cyclic_task = 0;
        return -3;
    }
    fprintf(stdout, "SDK: Cyclic task started.\n");
    return 0;
}

__attribute__((visibility("default"))) void sdk_stop_cyclic_task() {
    if (g_run_cyclic_task) {
        g_run_cyclic_task = 0;
        if (pthread_join(g_cyclic_task_thread_id, NULL)) {
            perror("SDK: Failed to join cyclic task thread");
        }
         fprintf(stdout, "SDK: Cyclic task stopped.\n");
    } else {
         fprintf(stdout, "SDK: Cyclic task was not running.\n");
    }
}

__attribute__((visibility("default"))) void sdk_cleanup() {
    sdk_stop_cyclic_task(); // Ensure task is stopped first

    if (g_master) {
        // Place slaves in PRE-OP before deactivating master (optional, good practice)
        // ecrt_master_application_time(g_master, 0); // Reset time
        // for all slaves config: ecrt_slave_config_sdo32(sc, 0x6040, 0, 6); // Shutdown
        // then wait a bit
        // This requires iterating all slave_configs, which would come from JSON.
        // For now, directly deactivate.

        fprintf(stdout, "SDK: Deactivating master...\n");
        ecrt_master_deactivate(g_master); // Stops PDO exchange
        fprintf(stdout, "SDK: Releasing master...\n");
        ecrt_release_master(g_master);
        g_master = NULL;
    }
    
    internal_cleanup_shm();
    // g_domain1_pd is invalid after master release, g_domain1 also.
    g_domain1_pd = NULL;
    g_domain1 = NULL; 
    g_sc_slave0 = NULL; // Configs are invalidated

    pthread_mutex_destroy(&g_shm_mutex); // Only if dynamically initialized
                                         // PTHREAD_MUTEX_INITIALIZER doesn't need destroy
                                         // but good practice if init changes.

    if (munlockall() == -1) { // Unlock memory
        perror("SDK: munlockall() failed");
    }

    fprintf(stdout, "SDK: Cleanup complete.\n");
}

__attribute__((visibility("default"))) int sdk_connect() {
    internal_create_shm();
    if (g_sdk_shm_ptr == MAP_FAILED || g_sdk_shm_ptr == NULL) {
        fprintf(stderr, "SDK: User Failed to create or map shared memory.\n");
        return -1; // SHM is critical
    }
    return 0;
}

__attribute__((visibility("default"))) int sdk_write(uint16_t slave_number, uint16_t index, uint8_t subindex, int32_t value) {
    if (!g_sdk_shm_ptr || g_sdk_shm_ptr == MAP_FAILED) {
        fprintf(stderr, "SDK: SHM not available for write.\n");
        return -1;
    }
    // This mapping would be dynamically generated from JSON.
    // Hardcoded for slave 0 and specific OD entries for now.
    pthread_mutex_lock(&g_shm_mutex);
    int result = 0;
    if (slave_number == 0) { // Assuming slave0_POS maps to slave_number 0
        switch (index) {
            case 0x6060: // operation_mode
                if (subindex == 0) g_sdk_shm_ptr->shm_slave0_rx_0x6060_operation_mode = value;
                else result = -3; // Invalid subindex
                break;
            case 0x60FF: // target_speed
                if (subindex == 0) g_sdk_shm_ptr->shm_slave0_rx_0x60ff_target_speed = value;
                else result = -3;
                break;
            case 0x6040: // control_word
                if (subindex == 0) g_sdk_shm_ptr->shm_slave0_rx_0x6040_control_word = value;
                else result = -3;
                break;
            default:
                result = -2; // Invalid index for write on slave 0
                break;
        }
    } else {
        result = -4; // Invalid slave number
    }
    pthread_mutex_unlock(&g_shm_mutex);

    if (result != 0) {
        fprintf(stderr, "SDK: sdk_write failed (slave:%u, idx:0x%X, sub:0x%X, err:%d)\n",
                slave_number, index, subindex, result);
    }
    return result;
}

__attribute__((visibility("default"))) int sdk_read(uint16_t slave_number, uint16_t index, uint8_t subindex, int32_t* value_ptr) {
    if (!g_sdk_shm_ptr || g_sdk_shm_ptr == MAP_FAILED) {
        fprintf(stderr, "SDK: SHM not available for read.\n");
        return -1;
    }
    if (!value_ptr) return -5; // Null pointer for output

    // This mapping would be dynamically generated from JSON.
    pthread_mutex_lock(&g_shm_mutex);
    int result = 0;
    if (slave_number == 0) {
        switch (index) {
            case 0x6041: // status_word
                if (subindex == 0) *value_ptr = g_sdk_shm_ptr->shm_slave0_tx_0x6041_status_word;
                else result = -3;
                break;
            case 0x603F: // error_code
                if (subindex == 0) *value_ptr = g_sdk_shm_ptr->shm_slave0_tx_0x603f_error_code;
                else result = -3;
                break;
            case 0x606C: // actual_velocity
                if (subindex == 0) *value_ptr = g_sdk_shm_ptr->shm_slave0_tx_0x606c_actual_velocity;
                else result = -3;
                break;
            case 0x503F: // customize_error_code (assuming this is a TxPDO)
                if (subindex == 0) *value_ptr = g_sdk_shm_ptr->shm_slave0_tx_0x503f_customize_error_code;
                else result = -3;
                break;
            // Allow reading RxPDOs as well, for verification/logging if needed
            case 0x6060: // operation_mode
                if (subindex == 0) *value_ptr = g_sdk_shm_ptr->shm_slave0_rx_0x6060_operation_mode;
                else result = -3;
                break;
            case 0x60FF: // target_speed
                if (subindex == 0) *value_ptr = g_sdk_shm_ptr->shm_slave0_rx_0x60ff_target_speed;
                else result = -3;
                break;
            case 0x6040: // control_word
                if (subindex == 0) *value_ptr = g_sdk_shm_ptr->shm_slave0_rx_0x6040_control_word;
                else result = -3;
                break;
            default:
                result = -2; // Invalid index for read on slave 0
                break;
        }
    } else {
        result = -4; // Invalid slave number
    }
    pthread_mutex_unlock(&g_shm_mutex);

    if (result != 0) {
        fprintf(stderr, "SDK: sdk_read failed (slave:%u, idx:0x%X, sub:0x%X, err:%d)\n",
                slave_number, index, subindex, result);
    }
    return result;
}

__attribute__((visibility("default"))) int sdk_get_slave_status(uint16_t slave_number, int32_t* online_status, int32_t* op_status, int32_t* al_state) {
    if (!g_sdk_shm_ptr || g_sdk_shm_ptr == MAP_FAILED) return -1;
    if (!online_status || !op_status || !al_state) return -2;

    // For now, only slave 0 is supported by this hardcoded status block
    pthread_mutex_lock(&g_shm_mutex);
    int result = 0;
    if (slave_number == 0) {
        // These are updated by the cyclic task periodically
        *online_status = g_sdk_shm_ptr->shm_slave0_online_status;
        *op_status = g_sdk_shm_ptr->shm_slave0_operational_status;
        *al_state = g_sdk_shm_ptr->shm_slave0_al_state;
    } else {
        result = -3; // Slave not found or status not available in SHM structure
    }
    pthread_mutex_unlock(&g_shm_mutex);
    
    if (result != 0) {
         fprintf(stderr, "SDK: sdk_get_slave_status failed for slave %u (err:%d)\n", slave_number, result);
    }
    return result;
}

// int sdk_set_core_affinity(int core_id) {
//    if (core_id < 0) {
//        fprintf(stderr, "SDK: Invalid core_id %d for affinity setting.\n", core_id);
//        return -1;
//    }
//    // TODO: Check max cores available?
//    g_core_affinity = core_id;
//    fprintf(stdout, "SDK: Core affinity for cyclic task set to %d. Will be applied when task starts.\n", core_id);
//    return 0;
//}

// Placeholder for JSON parsing logic (would be extensive)
// static int parse_json_config(const char* json_path) {
//    // Open and parse json_path
//    // Populate g_shm_name, TASK_FREQUENCY
//    // Populate slave configurations (VID/PID, position)
//    // Populate PDO mappings (g_pdo_offsets, domain1_regs, slaveX_pdo_entries, slaveX_pdos, slaveX_syncs)
//    // This is where the "dynamic generation" aspect lives.
//    return 0; // 0 on success
// } 