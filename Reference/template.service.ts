import { ApiError } from '../utils/errors.js';
import { ErrorCode } from '../utils/error-codes.js';
import fs from 'fs/promises';
import path from 'path';
import { exec } from 'child_process';
import { promisify } from 'util';
import { EthercatService } from './ethercat.service.js';
const execAsync = promisify(exec);

interface SyncInfo {
  index: number;
  direction: 'EC_DIR_OUTPUT' | 'EC_DIR_INPUT';
  pdoCount: number;
  pdoIndex: number;
  watchdog: 'EC_WD_ENABLE' | 'EC_WD_DISABLE';
}

interface SDOConfig {
  name: string;
  index: string;
  subindex: string;
  type: string;
  value: string;
}

interface SlaveConfig {
  master?: number;  // 主站索引，可选，默认为0
  position: string;
  name: string;
  vendorId: number;
  productCode: number;
  index: string | number;  // 从站索引
  vid: string;  // Vendor ID
  pid: string;  // Product ID
  rx_pdo: string;
  tx_pdo: string;
  rx_pdos?: PDOConfig[];
  tx_pdos?: PDOConfig[];
  pdo_mapping?: {
    rx_pdos: Array<{
      index: string;
      entries: Array<{
        index: string;
        subindex: string;
        name: string;
        type: string;
      }>;
      entryOffset: number;
    }>;
    tx_pdos: Array<{
      index: string;
      entries: Array<{
        index: string;
        subindex: string;
        name: string;
        type: string;
      }>;
      entryOffset: number;
    }>;
  };
  sdos?: SDOConfig[];
  syncs?: SyncInfo[];
  dc_config?: {
    assign_activate: string;
    sync0_cycle: number;
    sync0_shift: number;
    sync1_cycle: number;
    sync1_shift: number;
  };
}

interface PDOConfig {
  name: string;
  index: string;
  type: string;
  subindex?: string;
}

interface TemplateConfig {
  masterIndex: number;
  taskFrequency: number;
  ethercatDir: string;
  slaves: SlaveConfig[];
}

export class TemplateService {
  static async generateCTemplate(config: TemplateConfig): Promise<string> {
    try {
      console.log('有从站验证，生成模板中')
      // 验证从站配置
      const validation = await this.validateSlaves(config);
      if (!validation.isValid) {
        throw new ApiError(
          ErrorCode.SLAVE_VID_PID_MISMATCH,
          `从站配置验证失败:\n${validation.errors.join('\n')}`
        );
      }

      // 首先获取每个从站的同步管理器信息
      const syncInfos = await Promise.all(
        config.slaves.map(async (slave: SlaveConfig, index: number) => {
          try {
            const { stdout } = await execAsync(`ethercat cstruct | sed -n '/ec_sync_info_t slave_${slave.index}_syncs\\[\\]/,/};/p'`);
            if (!stdout.trim()) {
              throw new Error(`No sync info found for slave ${index} (position ${slave.index})`);
            }

            // 解析原始同步管理器配置
            let syncInfo = stdout.trim();
            
            // 检查是否有 RxPDOs 和 TxPDOs
            const hasRxPDOs = slave.rx_pdos && slave.rx_pdos.length > 0;
            const hasTxPDOs = slave.tx_pdos && slave.tx_pdos.length > 0;

            // 修改 SM2 (输出) 配置
            if (hasRxPDOs) {
              syncInfo = syncInfo.replace(
                /\{2,\s*EC_DIR_OUTPUT,\s*[0-9]+,\s*(?:NULL|slave_[0-9]+_pdos\s*\+\s*[0-9]+),/,
                `{2, EC_DIR_OUTPUT, 1, slave${index}_pdos + 0,`
              );
            }

            // 修改 SM3 (输入) 配置
            if (hasTxPDOs) {
              syncInfo = syncInfo.replace(
                /\{3,\s*EC_DIR_INPUT,\s*[0-9]+,\s*(?:NULL|slave_[0-9]+_pdos\s*\+\s*[0-9]+),/,
                `{3, EC_DIR_INPUT, 1, slave${index}_pdos + 1,`
              );
            }

            // 修改变量名以匹配命名规则
            syncInfo = syncInfo
              .replace(
                `ec_sync_info_t slave_${slave.index}_syncs[]`,
                `ec_sync_info_t slave${index}_syncs[]`
              )
              .replace(
                new RegExp(`slave_${slave.index}_pdos`, 'g'),
                `slave${index}_pdos`
              );

            return {
              index,
              syncInfo
            } as SyncInfo;
          } catch (error) {
            // 如果是ApiError，直接往上抛
            if (error instanceof ApiError) {
              throw error;
            }
            throw new ApiError(
              ErrorCode.TEMPLATE_ERROR,
              `获取从站${index}同步信息失败: ${error.message}`
            );
          }
        })
      );

      let template = '';
      template += this.generateHeaders();
      template += this.generateShmStructure(config);
      template += this.generateAppParams(config);
      template += this.generateEtherCATConfig(config);
      template += this.generatePDOMapping(config, syncInfos);
      template += this.generateShmFunctions();
      template += this.generateCyclicTask(config);
      template += this.generateMainFunction(config);
      
      // 添加过滤步骤
      template = this.filterZeroIndexPDOs(template);
      console.log('过滤结束，返回c代码')
      return template;
    } catch (error) {
      // 如果是ApiError，直接往上抛，保持原始错误码和消息
      if (error instanceof ApiError) {
        throw error;
      }
      // 其他错误包装成模板错误
      throw new ApiError(
        ErrorCode.TEMPLATE_ERROR,
        `生成模板失败: ${error.message}`
      );
    }
  }

  static async generateCTemplateWithoutValidation(config: TemplateConfig): Promise<string> {
    console.log('无从站验证，生成模板中')
    try {
      let template = '';
      template += this.generateHeaders();
      template += this.generateShmStructure(config);
      template += this.generateAppParams(config);
      template += this.generateEtherCATConfig(config);

      // 创建空的 syncInfos 数组
      const syncInfos: SyncInfo[] = config.slaves.map((slave, index) => ({
        index,
        direction: 'EC_DIR_OUTPUT',
        pdoCount: 0,
        pdoIndex: 0,
        watchdog: 'EC_WD_DISABLE'
      }));

      template += this.generatePDOMapping(config, syncInfos);  // 传入 syncInfos
      template += this.generateShmFunctions();
      template += this.generateCyclicTask(config);
      template += this.generateMainFunction(config);

      // 添加过滤步骤
      template = this.filterZeroIndexPDOs(template);
      console.log('过滤结束，返回c代码')
      
      return template;
    } catch (error) {
      // 如果是ApiError，直接往上抛，保持原始错误码和消息
      if (error instanceof ApiError) {
        throw error;
      }
      // 其他错误包装成模板错误
      throw new ApiError(
        ErrorCode.TEMPLATE_ERROR,
        `生成模板失败: ${error.message}`
      );
    }
  }

  /**
   * 验证实际从站数量与配置是否一致
   * @param config JSON配置
   * @returns Promise<{isValid: boolean, errors: string[]}>
   */
  public static async validateSlaves(config: TemplateConfig): Promise<{isValid: boolean, errors: string[]}> {
    try {
      // 获取实际从站状态
      const slaveStatus = await EthercatService.getAllSlavesStatus(1);
      const actualSlaves = slaveStatus.slaves || [];
      const errors: string[] = [];

      console.log(`检查${config.masterIndex}主站下的从站列表`)
      // 只获取指定主站下的从站
      const targetMasterSlaves = actualSlaves.filter(slave => slave.master === config.masterIndex);
      
      if (targetMasterSlaves.length === 0) {
        errors.push(`未找到主站${config.masterIndex}的从站列表`);
        return { isValid: false, errors };
      }

      // 检查每个配置的从站
      for (let i = 0; i < config.slaves.length; i++) {
        const configuredSlave = config.slaves[i];
        // 将配置的索引转换为数字进行比较
        const configIndex = parseInt(configuredSlave.index.toString());

        // 在目标主站下查找从站
        const actualSlave = targetMasterSlaves.find(s => s.index === configIndex);
        if (!actualSlave) {
          errors.push(`从站 ${i} (位置 ${config.masterIndex}:${configIndex}): 在主站${config.masterIndex}下未找到从站${configIndex}`);
          continue;
        }

        // 验证 VID
        const configVid = configuredSlave.vid.toLowerCase().replace('0x', '');
        const actualVid = actualSlave.vendorId.toLowerCase().replace('0x', '');
        if (configVid !== actualVid) {
          errors.push(
            `从站 ${i} (位置 ${config.masterIndex}:${configIndex}): VID不匹配 ` +
            `(配置: 0x${configVid}, 实际: 0x${actualVid})`
          );
        }

        // 验证 PID
        const configPid = configuredSlave.pid.toLowerCase().replace('0x', '');
        const actualPid = actualSlave.productCode.toLowerCase().replace('0x', '');
        if (configPid !== actualPid) {
          errors.push(
            `从站 ${i} (位置 ${config.masterIndex}:${configIndex}): PID不匹配 ` +
            `(配置: 0x${configPid}, 实际: 0x${actualPid})`
          );
        }
      }

      // 添加调试日志
      console.log('Validation results:', {
        actualSlaves: actualSlaves.map(s => ({
          master: s.master,
          index: s.index,
          vendorId: s.vendorId,
          productCode: s.productCode,
          state: s.state
        })),
        targetMasterSlaves: targetMasterSlaves.map(s => ({
          master: s.master,
          index: s.index,
          vendorId: s.vendorId,
          productCode: s.productCode,
          state: s.state
        })),
        configuredSlaves: config.slaves.map(s => ({
          master: config.masterIndex,
          index: s.index,
          vid: s.vid,
          pid: s.pid
        })),
        errors
      });

      return {
        isValid: errors.length === 0,
        errors
      };
    } catch (error) {
      console.error('Failed to validate slaves:', error);
      throw error;
    }
  }

  private static generateHeaders(): string {
    return `
#include <errno.h>
#include <signal.h>
#include <stdio.h>
#include <string.h>
#include <sys/resource.h>
#include <sys/time.h>
#include <sys/types.h>
#include <unistd.h>
#include <sys/mman.h>
#include <fcntl.h>
#include <sched.h>
#include <sys/stat.h>
#include <sys/shm.h>
#include <time.h>
#include <pthread.h>  // 添加pthread头文件

#include "ecrt.h"

// Forward declarations
void cleanup_shm(void);
void signal_handler(int sig);
void check_slave_config_states(void);

// 定义PDO配置线程的参数结构
typedef struct {
    ec_slave_config_t *slave_config;
    ec_sync_info_t *sync_info;
    int *result;
} config_thread_param_t;

// PDO配置线程函数
void* config_slave_thread(void *arg) {
    config_thread_param_t *param = (config_thread_param_t *)arg;
    *(param->result) = ecrt_slave_config_pdos(param->slave_config, EC_END, param->sync_info);
    return NULL;
}

// 定义DC配置线程的参数结构
typedef struct {
    ec_slave_config_t *slave_config;
    uint16_t assign_activate;
    uint32_t sync0_cycle;
    uint32_t sync0_shift;
    uint32_t sync1_cycle;
    uint32_t sync1_shift;
    int *result;
} dc_config_thread_param_t;

// DC配置线程函数
void* dc_config_thread(void *arg) {
    dc_config_thread_param_t *param = (dc_config_thread_param_t *)arg;
    int ret = ecrt_slave_config_dc(
        param->slave_config,
        param->assign_activate,
        param->sync0_cycle,
        param->sync0_shift,
        param->sync1_cycle,
        param->sync1_shift
    );
    *(param->result) = ret;
    return NULL;
}

// Global control flags
static int run = 1;  // 控制主循环的标志
static int last_cycle = 0;  // 标记最后一个循环

// Signal handler implementation
void signal_handler(int sig) {
    printf("\\nSignal %d received, will exit after next cycle...\\n", sig);
    last_cycle = 1;  // 设置最后一个循环标志
}

/* Time definitions */
#define NSEC_PER_SEC (1000000000L)
#define TIMESPEC2NS(T) ((uint64_t) (T).tv_sec * NSEC_PER_SEC + (T).tv_nsec)
#define CLOCK_TO_USE CLOCK_MONOTONIC
`;
  }

  private static generateShmStructure(config: any): string {
    const shmFileName = `${config.id}_${config.name}_shm`;

    let struct = `
/* Shared memory configuration */
#define ETHERCAT_SHM_FILE "${shmFileName}"
#define ETHERCAT_SHM_SIZE (sizeof(ethercat_shm_t))

/* Shared memory structure */
typedef struct {
`;

    // 为每个从站添加状态变量
    config.slaves.forEach((slave: any, index: number) => {
        struct += `    int shm_slave${index}_online_status; /* 从站${index}在线状态 */\n`;
        struct += `    int shm_slave${index}_operational_status; /* 从站${index}运行状态 */\n`;
        struct += `    int shm_slave${index}_al_state; /* 从站${index}AL状态 */\n\n`;
        
        // 添加PDO变量，跳过index为0的PDO
        if (slave.rx_pdos) {
            for (const pdo of slave.rx_pdos) {
                const pdoIndex = pdo.index.replace('0x', '');
                if (pdoIndex === '0000' || pdoIndex === '0') {
                    console.log(`[Slave ${index}] 跳过索引为0的RX PDO共享内存定义`);
                    continue;
                }
                const varName = this.sanitizeVariableName(pdo, index, 'rx');
                const comment = pdo.comment ? ` /* ${pdo.comment} */` : '';
                struct += `    int shm_slave${index}_rx_${varName};${comment}\n`;
            }
        }

        if (slave.tx_pdos) {
            for (const pdo of slave.tx_pdos) {
                const pdoIndex = pdo.index.replace('0x', '');
                if (pdoIndex === '0000' || pdoIndex === '0') {
                    console.log(`[Slave ${index}] 跳过索引为0的TX PDO共享内存定义`);
                    continue;
                }
                const varName = this.sanitizeVariableName(pdo, index, 'tx');
                const comment = pdo.comment ? ` /* ${pdo.comment} */` : '';
                struct += `    int shm_slave${index}_tx_${varName};${comment}\n`;
            }
        }
    });

    struct += `} ethercat_shm_t;\n\nstatic ethercat_shm_t *ethercat_shm = NULL;\n`;
    return struct;
  }

  private static sanitizeVariableName(pdo: any, slaveIndex: number, direction: 'rx' | 'tx'): string {
    if (!pdo || !pdo.index || !pdo.name) {
      throw new Error('Invalid PDO data');
    }
    // 构造变量名: shm_slave{index}_rx/tx_index_name
    const baseName = `shm_slave${slaveIndex}_${direction}_${pdo.index.toLowerCase()}_${pdo.name.toLowerCase()}`;
    return baseName.replace(/[^a-z0-9_]/g, '_');
  }

  private static generateShmFunctions(): string {
    return `
void create_shm() {
    // Create and open shared memory
    int fd = shm_open(ETHERCAT_SHM_FILE, O_CREAT | O_RDWR, 0666);
    if (fd < 0) {
        perror("shm_open failed");
        exit(EXIT_FAILURE);
    }

    // Set the size of shared memory
    if (ftruncate(fd, ETHERCAT_SHM_SIZE) < 0) {
        perror("ftruncate failed");
        exit(EXIT_FAILURE);
    }

    // Map shared memory
    ethercat_shm = (ethercat_shm_t *)mmap(NULL, ETHERCAT_SHM_SIZE, 
                                         PROT_READ | PROT_WRITE, 
                                         MAP_SHARED, fd, 0);
    if (ethercat_shm == MAP_FAILED) {
        perror("mmap failed");
        exit(EXIT_FAILURE);
    }

    // Initialize shared memory to zero
    memset(ethercat_shm, 0, ETHERCAT_SHM_SIZE);
    
    // Close file descriptor (mapping remains valid)
    close(fd);
}

void cleanup_shm() {
    if (ethercat_shm != NULL) {
        munmap(ethercat_shm, ETHERCAT_SHM_SIZE);
        shm_unlink(ETHERCAT_SHM_FILE);
    }
}
`;
  }

  private static generateAppParams(config: any): string {
    return `
/* Application Parameters */
#define TASK_FREQUENCY ${config.taskFrequency || 1000} /*Hz*/
#define PERIOD_NS (NSEC_PER_SEC / TASK_FREQUENCY)

/* 状态更新参数 */
#define STATUS_UPDATE_INTERVAL_MS 1000 /* 固定的状态更新间隔，毫秒 */
/* 根据任务频率自动计算需要多少个周期才能达到指定的时间间隔 */
#define STATUS_UPDATE_PERIOD ((TASK_FREQUENCY * STATUS_UPDATE_INTERVAL_MS) / 1000)
`;
  }

  private static generateEtherCATConfig(config: TemplateConfig): string {
    let conf = `\n/* EtherCAT configurations */\n`;
    conf += `#define MASTER_INDEX ${config.masterIndex || 0}\n\n`;
    conf += `static ec_master_t *master = NULL;\n`;
    conf += `static ec_master_state_t master_state = {};\n`;
    conf += `static ec_domain_t *domain1 = NULL;\n`;
    conf += `static ec_domain_state_t domain1_state = {};\n`;
    
    // 为每个从站创建独立的配置和状态对象
    config.slaves.forEach((slave: any, index: number) => {
        conf += `static ec_slave_config_t *sc_slave${index} = NULL;\n`;
        conf += `static ec_slave_config_state_t sc_slave${index}_state = {};\n`;
    });
    
    conf += `static uint8_t *domain1_pd = NULL;\n\n`;

    // 为每个从站生成位置和VID_PID定义
    config.slaves.forEach((slave: any, index: number) => {
        conf += `#define slave${index}_POS 0,${slave.index}\n`;
        conf += `#define slave${index}_VID_PID ${slave.vid},${slave.pid}\n`;
    });

    return conf;
  }

  private static generatePDOMapping(config: any, syncInfos: SyncInfo[]): string {
    let mapping = `\n/* PDO mapping */\n`;
    
    // 生成 SDO 数据定义
    mapping += this.generateSDODefinitions(config);

    // 生成 PDO 偏移结构
    mapping += this.generatePDOOffsets(config);

    // 生成 PDO 条目注册
    mapping += this.generatePDOEntryRegistration(config);

    // 为每个从站生成 PDO 条目信息
    config.slaves.forEach((slave: any, index: number) => {
      // 生成PDO条目信息
      mapping += this.generatePDOEntries(slave, index);

      // 生成PDO信息
      mapping += this.generatePDOInfo(slave, index);

      // 生成同步管理器配置
      mapping += this.generateSyncManagerConfig(slave, index);
    });

    return mapping;
  }

  private static generateSDODefinitions(config: any): string {
    let sdoDefinitions = `\n/* SDO data definitions */\n`;
    config.slaves.forEach((slave: any, index: number) => {
      if (slave.sdos && slave.sdos.length > 0) {
        console.log(`Found ${slave.sdos.length} SDOs for slave ${index}`);
        sdoDefinitions += `// SDO data for slave ${index}\n`;
        slave.sdos.forEach((sdo: SDOConfig) => {
          console.log(`Processing SDO: ${JSON.stringify(sdo)}`);
          const varName = `sdo_${index}_${sdo.index.replace('0x', '')}_${sdo.subindex}_data`;
          const type = sdo.type === 'uint8' ? 'uint8_t' : 
                      sdo.type === 'uint16' ? 'uint16_t' : 'uint32_t';
          const line = `static ${type} ${varName} = ${sdo.value};  // ${sdo.name} (${sdo.index}:${sdo.subindex})\n`;
          console.log(`Generated line: ${line}`);
          sdoDefinitions += line;
        });
        sdoDefinitions += '\n';
      } else {
        console.log(`No SDOs found for slave ${index}`);
      }
    });
    return sdoDefinitions;
  }

  private static generatePDOOffsets(config: any): string {
    let offsets = `static struct {\n`;
    config.slaves.forEach((slave: any, index: number) => {
        if (slave.rx_pdos) {
            for (const pdo of slave.rx_pdos) {
                const pdoIndex = pdo.index.replace('0x', '');
                if (pdoIndex === '0000' || pdoIndex === '0') {
                    console.log(`[Slave ${index}] 跳过索引为0的RX PDO偏移量定义`);
                    continue;
                }
                const varName = this.sanitizeVariableName(pdo, index, 'rx');
                if (pdo.type === 'bool') {
                    offsets += `    unsigned int pdo_slave${index}_rx_${varName}_off;\n`;
                    offsets += `    unsigned int pdo_slave${index}_rx_${varName}_bit;\n`;
                } else {
                    offsets += `    unsigned int pdo_slave${index}_rx_${varName};\n`;
                }
            }
        }

        if (slave.tx_pdos) {
            for (const pdo of slave.tx_pdos) {
                const pdoIndex = pdo.index.replace('0x', '');
                if (pdoIndex === '0000' || pdoIndex === '0') {
                    console.log(`[Slave ${index}] 跳过索引为0的TX PDO偏移量定义`);
                    continue;
                }
                const varName = this.sanitizeVariableName(pdo, index, 'tx');
                if (pdo.type === 'bool') {
                    offsets += `    unsigned int pdo_slave${index}_tx_${varName}_off;\n`;
                    offsets += `    unsigned int pdo_slave${index}_tx_${varName}_bit;\n`;
                } else {
                    offsets += `    unsigned int pdo_slave${index}_tx_${varName};\n`;
                }
            }
        }
    });
    offsets += `} offset;\n\n`;
    return offsets;
  }

  private static generatePDOEntryRegistration(config: any): string {
    let entries = `const static ec_pdo_entry_reg_t domain1_regs[] = {\n`;
    config.slaves.forEach((slave: any, index: number) => {
        if (slave.rx_pdos) {
            for (const pdo of slave.rx_pdos) {
                const pdoIndex = pdo.index.replace('0x', '');
                if (pdoIndex === '0000' || pdoIndex === '0') {
                    console.log(`[Slave ${index}] 跳过索引为0的RX PDO注册`);
                    continue;
                }
                const varName = this.sanitizeVariableName(pdo, index, 'rx');
                if (pdo.type === 'bool') {
                    entries += `    {slave${index}_POS, slave${index}_VID_PID, ${pdo.index}, ${pdo.subindex}, &offset.pdo_slave${index}_rx_${varName}_off, &offset.pdo_slave${index}_rx_${varName}_bit},\n`;
                } else {
                    entries += `    {slave${index}_POS, slave${index}_VID_PID, ${pdo.index}, ${pdo.subindex}, &offset.pdo_slave${index}_rx_${varName}},\n`;
                }
            }
        }

        if (slave.tx_pdos) {
            for (const pdo of slave.tx_pdos) {
                const pdoIndex = pdo.index.replace('0x', '');
                if (pdoIndex === '0000' || pdoIndex === '0') {
                    console.log(`[Slave ${index}] 跳过索引为0的TX PDO注册`);
                    continue;
                }
                const varName = this.sanitizeVariableName(pdo, index, 'tx');
                if (pdo.type === 'bool') {
                    entries += `    {slave${index}_POS, slave${index}_VID_PID, ${pdo.index}, ${pdo.subindex}, &offset.pdo_slave${index}_tx_${varName}_off, &offset.pdo_slave${index}_tx_${varName}_bit},\n`;
                } else {
                    entries += `    {slave${index}_POS, slave${index}_VID_PID, ${pdo.index}, ${pdo.subindex}, &offset.pdo_slave${index}_tx_${varName}},\n`;
                }
            }
        }
    });
    entries += `    {}\n};\n\n`;
    return entries;
  }

  private static generatePDOEntries(slave: any, index: number): string {
    let entries = `\nstatic ec_pdo_entry_info_t slave${index}_pdo_entries[] = {\n`;
    let entryIndex = 0;
    
    // 处理 RX PDOs
    if (slave.rx_pdos) {
        slave.rx_pdos.forEach((pdo: any) => {
            const pdoIndex = pdo.index.replace('0x', '');  // 移除前缀
            if (pdoIndex === '0000' || pdoIndex === '0') {
                // 对于 index 为 0 的 PDO，使用与原始类型相同大小的填充位
                const bitSize = this.getTypeBits(pdo);
                entries += `    {0x0000, 0, ${bitSize}},  /* 填充位 (${pdo.type}) */\n`;
                console.log(`[Slave ${index}] 发现索引为0的PDO，使用${bitSize}位填充位代替`);
            } else {
                entries += `    {0x${pdoIndex}, ${pdo.subindex || 0}, ${this.getTypeBits(pdo)}},  /* ${pdo.name} */\n`;
            }
        });
    }

    // 处理 TX PDOs
    if (slave.tx_pdos) {
        slave.tx_pdos.forEach((pdo: any) => {
            const pdoIndex = pdo.index.replace('0x', '');  // 移除前缀
            if (pdoIndex === '0000' || pdoIndex === '0') {
                // 对于 index 为 0 的 PDO，使用与原始类型相同大小的填充位
                const bitSize = this.getTypeBits(pdo);
                entries += `    {0x0000, 0, ${bitSize}},  /* 填充位 (${pdo.type}) */\n`;
                console.log(`[Slave ${index}] 发现索引为0的PDO，使用${bitSize}位填充位代替`);
            } else {
                entries += `    {0x${pdoIndex}, ${pdo.subindex || 0}, ${this.getTypeBits(pdo)}},  /* ${pdo.name} */\n`;
            }
        });
    }

    entries += `};\n`;
    return entries;
  }

  private static generatePDOInfo(slave: any, index: number): string {
    let pdoInfo = `\nstatic ec_pdo_info_t slave${index}_pdos[] = {\n`;
    let entryOffset = 0;

    // 检查pdo_mapping是否存在且至少有一个非空的PDO数组
    const hasPdoMapping = slave.pdo_mapping && 
                         Array.isArray(slave.pdo_mapping.rx_pdos) && 
                         Array.isArray(slave.pdo_mapping.tx_pdos) &&
                         slave.pdo_mapping.rx_pdos.length > 0 && 
                         slave.pdo_mapping.tx_pdos.length > 0;

    // console.log(`[generatePDOInfo] hasPdoMapping: ${hasPdoMapping}`);
    // console.log(`[generatePDOInfo] slave.pdo_mapping: ${JSON.stringify(slave.pdo_mapping)}`);

    if (hasPdoMapping) {
        // console.log(`[generatePDOInfo] 使用 pdo_mapping 生成 PDO 信息`);
        
        // 处理RxPDO
        if (slave.pdo_mapping.rx_pdos.length > 0) {
            for (const pdo of slave.pdo_mapping.rx_pdos) {
                // 跳过 index 为 0 的 PDO 映射
                if (pdo.index === '0x0000' || pdo.index === '0x00000000') {
                    console.log(`[Slave ${index}] 跳过索引为0的RX PDO映射信息`);
                    continue;
                }
                const pdoIndexHex = pdo.index.replace('0x', '');
                pdoInfo += `    {0x${pdoIndexHex}, ${pdo.entries.length}, slave${index}_pdo_entries + ${entryOffset}},  /* RxPDO */\n`;
                entryOffset += pdo.entries.length;
            }
        }

        // 处理TxPDO
        if (slave.pdo_mapping.tx_pdos.length > 0) {
            for (const pdo of slave.pdo_mapping.tx_pdos) {
                // 跳过 index 为 0 的 PDO 映射
                if (pdo.index === '0x0000' || pdo.index === '0x00000000') {
                    console.log(`[Slave ${index}] 跳过索引为0的TX PDO映射信息`);
                    continue;
                }
                const pdoIndexHex = pdo.index.replace('0x', '');
                pdoInfo += `    {0x${pdoIndexHex}, ${pdo.entries.length}, slave${index}_pdo_entries + ${entryOffset}},  /* TxPDO */\n`;
                entryOffset += pdo.entries.length;
            }
        }
    } else {
        // console.log(`[generatePDOInfo] 使用原有方法生成 PDO 信息`);
        
        // 计算RxPDO条目数量
        const rxPdoCount = slave.rx_pdos ? slave.rx_pdos.filter(pdo => {
            const pdoIndex = pdo.index.replace('0x', '');
            return pdoIndex !== '0000' && pdoIndex !== '0';
        }).length : 0;

        // 计算TxPDO条目数量
        const txPdoCount = slave.tx_pdos ? slave.tx_pdos.filter(pdo => {
            const pdoIndex = pdo.index.replace('0x', '');
            return pdoIndex !== '0000' && pdoIndex !== '0';
        }).length : 0;

        // 处理RxPDO
        if (slave.rx_pdo) {
            const rxPdoIndices = slave.rx_pdo.split(',').map((p: string) => p.trim());
            for (const pdoIndex of rxPdoIndices) {
                if (pdoIndex === '0x0000' || pdoIndex === '0x00000000') {
                    console.log(`[Slave ${index}] 跳过索引为0的RX PDO映射信息`);
                    continue;
                }
                const pdoIndexHex = pdoIndex.replace('0x', '');
                pdoInfo += `    {0x${pdoIndexHex}, ${rxPdoCount}, slave${index}_pdo_entries + ${entryOffset}},  /* RxPDO */\n`;
            }
        }

        // 更新偏移量，指向TxPDO条目的起始位置
        entryOffset = rxPdoCount;

        // 处理TxPDO
        if (slave.tx_pdo) {
            const txPdoIndices = slave.tx_pdo.split(',').map((p: string) => p.trim());
            for (const pdoIndex of txPdoIndices) {
                if (pdoIndex === '0x0000' || pdoIndex === '0x00000000') {
                    console.log(`[Slave ${index}] 跳过索引为0的TX PDO映射信息`);
                    continue;
                }
                const pdoIndexHex = pdoIndex.replace('0x', '');
                pdoInfo += `    {0x${pdoIndexHex}, ${txPdoCount}, slave${index}_pdo_entries + ${entryOffset}},  /* TxPDO */\n`;
            }
        }
    }

    pdoInfo += `};\n`;
    return pdoInfo;
  }

  private static generateSyncManagerConfig(slave: any, index: number): string {
    let syncConfig = `\nstatic ec_sync_info_t slave${index}_syncs[] = {\n`;
    
    // 检查syncs是否存在且不为空
    const hasSyncs = slave.syncs && Array.isArray(slave.syncs) && slave.syncs.length > 0;
    
    // console.log(`[generateSyncManagerConfig] hasSyncs: ${hasSyncs}`);
    
    if (hasSyncs) {
      // console.log(`[generateSyncManagerConfig] 使用 syncs 生成同步管理器配置`);
      
      // 累计PDO索引偏移量
      let pdoOffset = 0;
      
      // 使用从站中定义的 syncs 数组生成配置
      for (const sync of slave.syncs) {
        // 转换方向
        const direction = sync.direction === "OUTPUT" ? "EC_DIR_OUTPUT" : "EC_DIR_INPUT";
        
        // 转换看门狗
        const watchdog = sync.watchdog === "ENABLE" ? "EC_WD_ENABLE" : "EC_WD_DISABLE";
        
        // 计算PDO数量
        const pdoCount = Array.isArray(sync.pdos) ? sync.pdos.length : 0;
        
        // 生成配置行
        if (pdoCount > 0) {
          // 有PDO映射，使用偏移量
          syncConfig += `    {${sync.index}, ${direction}, ${pdoCount}, slave${index}_pdos + ${pdoOffset}, ${watchdog}},\n`;
          // 更新偏移量
          pdoOffset += pdoCount;
        } else {
          // 没有PDO映射，使用NULL
          syncConfig += `    {${sync.index}, ${direction}, 0, NULL, ${watchdog}},\n`;
        }
      }
    } else {
      // console.log(`[generateSyncManagerConfig] 使用原有方法生成同步管理器配置`);
      
      // 默认的同步管理器配置
      syncConfig += `    {0, EC_DIR_OUTPUT, 0, NULL, EC_WD_DISABLE},\n`;
      syncConfig += `    {1, EC_DIR_INPUT, 0, NULL, EC_WD_DISABLE},\n`;
      
      let pdoOffset = 0;
      
      // 处理RxPDO (SM2)
      if (slave.rx_pdos && slave.rx_pdos.length > 0) {
        const rxPdoCount = slave.rx_pdo ? slave.rx_pdo.split(',').length : 0;
        syncConfig += `    {2, EC_DIR_OUTPUT, ${rxPdoCount}, slave${index}_pdos + ${pdoOffset}, EC_WD_ENABLE},\n`;
        pdoOffset += rxPdoCount;
      } else {
        syncConfig += `    {2, EC_DIR_OUTPUT, 0, NULL, EC_WD_ENABLE},\n`;
      }
      
      // 处理TxPDO (SM3)
      if (slave.tx_pdos && slave.tx_pdos.length > 0) {
        const txPdoCount = slave.tx_pdo ? slave.tx_pdo.split(',').length : 0;
        syncConfig += `    {3, EC_DIR_INPUT, ${txPdoCount}, slave${index}_pdos + ${pdoOffset}, EC_WD_DISABLE},\n`;
      } else {
        syncConfig += `    {3, EC_DIR_INPUT, 0, NULL, EC_WD_DISABLE},\n`;
      }
    }
    
    // 终止同步管理器列表
    syncConfig += `    {0xff}\n};\n`;
    return syncConfig;
  }

  private static groupPdosByIndex(pdos: PDOConfig[]): { [key: string]: PDOConfig[] } {
    const groups: { [key: string]: PDOConfig[] } = {};
    
    pdos.forEach(pdo => {
      // 获取PDO映射索引
      const pdoIndex = pdo.index.substring(0, pdo.index.indexOf(',')) || pdo.index;
      if (!groups[pdoIndex]) {
        groups[pdoIndex] = [];
      }
      groups[pdoIndex].push(pdo);
    });
    
    return groups;
  }

  private static getTypeBits(pdo: any): number {
    // 如果有 bitlen，直接使用
    if (pdo.bitlen !== undefined) {
      return pdo.bitlen;
    }

    // 否则根据类型提供默认值
    switch (pdo.type.toLowerCase()) {
      case 'bool':
        return 1;
      case 'int8':
      case 'uint8':
        return 8;
      case 'uint16':
      case 'int16':
        return 16;
      case 'uint32':
      case 'int32':
        return 32;
      case 'uint64':
      case 'int64':
      case 'double':
        return 64;
      default:
        return 16;  // 默认使用 16 位
    }
  }

  private static getPDOReadFunction(type: string): string {
    switch (type.toLowerCase()) {
      case 'bool':
        return 'EC_READ_BIT';
      case 'uint8':
        return 'EC_READ_U8';
      case 'int8':
        return 'EC_READ_S8';
      case 'uint16':
        return 'EC_READ_U16';
      case 'int16':
        return 'EC_READ_S16';
      case 'uint32':
        return 'EC_READ_U32';
      case 'int32':
        return 'EC_READ_S32';
      case 'uint64':
        return 'EC_READ_U64';
      case 'int64':
        return 'EC_READ_S64';
      case 'double':
        return 'EC_READ_LREAL';
      default:
        return 'EC_READ_U16';
    }
  }

  private static getPDOWriteFunction(type: string): string {
    switch (type.toLowerCase()) {
      case 'bool':
        return 'EC_WRITE_BIT';
      case 'uint8':
        return 'EC_WRITE_U8';
      case 'int8':
        return 'EC_WRITE_S8';
      case 'uint16':
        return 'EC_WRITE_U16';
      case 'int16':
        return 'EC_WRITE_S16';
      case 'uint32':
        return 'EC_WRITE_U32';
      case 'int32':
        return 'EC_WRITE_S32';
      case 'uint64':
        return 'EC_WRITE_U64';
      case 'int64':
        return 'EC_WRITE_S64';
      case 'double':
        return 'EC_WRITE_LREAL';
      default:
        return 'EC_WRITE_U16';
    }
  }

  private static generateCyclicTask(config: any): string {
    let task = `
void cyclic_task(void)
{
    static int cycle_counter = 0;
    static struct timespec wakeupTime;
    static int initialized = 0;

    if (!initialized) {
        clock_gettime(CLOCK_TO_USE, &wakeupTime);
        initialized = 1;
    }

    wakeupTime.tv_nsec += PERIOD_NS;
    while (wakeupTime.tv_nsec >= NSEC_PER_SEC) {
        wakeupTime.tv_nsec -= NSEC_PER_SEC;
        wakeupTime.tv_sec++;
    }

    clock_nanosleep(CLOCK_TO_USE, TIMER_ABSTIME, &wakeupTime, NULL);

    ecrt_master_application_time(master, TIMESPEC2NS(wakeupTime));

    ecrt_master_receive(master);
    
    // 添加错误检查
    if (ecrt_master_state(master, &master_state)) {
        run = 0;  // 如果出现错误，触发程序退出
        fprintf(stderr, "Failed to get master state.\\n");
        return;
    }
    
    ecrt_domain_process(domain1);

    if (cycle_counter) {
        cycle_counter--;
    } else {
        cycle_counter = STATUS_UPDATE_PERIOD;
        ${config.slaves.map((_, index) => `
          ecrt_slave_config_state(sc_slave${index}, &sc_slave${index}_state);
          ethercat_shm->shm_slave${index}_online_status = sc_slave${index}_state.online;
          ethercat_shm->shm_slave${index}_operational_status = sc_slave${index}_state.operational;
          ethercat_shm->shm_slave${index}_al_state = sc_slave${index}_state.al_state;
      `).join('\n')}
    }

    // 更新从站状态
`;

//     // 为每个从站添加状态更新代码
//     config.slaves.forEach((slave: any, index: number) => {
//         task += `
//     // 获取从站${index}状态
//     ecrt_slave_config_state(sc_slave${index}, &sc_slave${index}_state);
        
//     // 打印从站状态
//     //printf("slave %d: State 0x%02X.\\n", ${index}, sc_slave${index}_state.al_state);
//     //printf("slave %d: %s.\\n", ${index}, sc_slave${index}_state.online ? "online" : "offline");
//     //printf("slave %d: %soperational.\\n", ${index}, sc_slave${index}_state.operational ? "" : "Not ");
    
//     // 更新从站${index}状态到共享内存
//     ethercat_shm->shm_slave${index}_online_status = sc_slave${index}_state.online;
//     ethercat_shm->shm_slave${index}_operational_status = sc_slave${index}_state.operational;
//     ethercat_shm->shm_slave${index}_al_state = sc_slave${index}_state.al_state;

//     // 从共享内存读取并打印状态
//     //printf("==== 共享内存 - slave %d: State 0x%02X, %s, %soperational\\n", 
//     //       ${index}, 
//     //       ethercat_shm->shm_slave${index}_al_state,
//     //       ethercat_shm->shm_slave${index}_online_status ? "online" : "offline",
//     //       ethercat_shm->shm_slave${index}_operational_status ? "" : "Not ");
    
//     //ethercat_shm->shm_slave${index}_tx_shm_slave0_tx_0x603f_error_code = 1;

//     // 从共享内存中读取并打印error_code
//     //printf("==== 共享内存 - slave %d: error_code = %d\\n", 
//     //   ${index},
//     //   ethercat_shm->shm_slave${index}_tx_shm_slave0_tx_0x603f_error_code);
// `;
//     });

    // 添加PDO更新代码
    task += `
    // Update shared memory with status
${config.slaves.map((slave: any, index: number) => 
    slave.tx_pdos?.map((pdo: any) => {
        const pdoIndex = pdo.index.replace('0x', '');
        if (pdoIndex === '0000' || pdoIndex === '0') {
            return ''; // 跳过index为0的PDO
        }
        const varName = this.sanitizeVariableName(pdo, index, 'tx');
        if (pdo.type === 'bool') {
            return `    ethercat_shm->shm_slave${index}_tx_${varName} = EC_READ_BIT(domain1_pd + offset.pdo_slave${index}_tx_${varName}_off, offset.pdo_slave${index}_tx_${varName}_bit);`;
        } else {
            const readFunc = this.getPDOReadFunction(pdo.type);
            return `    ethercat_shm->shm_slave${index}_tx_${varName} = ${readFunc}(domain1_pd + offset.pdo_slave${index}_tx_${varName});`;
        }
    }).filter(line => line !== '').join('\n')
).join('\n')}

    // Write to EtherCAT
${config.slaves.map((slave: any, index: number) => 
    slave.rx_pdos?.map((pdo: any) => {
        const pdoIndex = pdo.index.replace('0x', '');
        if (pdoIndex === '0000' || pdoIndex === '0') {
            return ''; // 跳过index为0的PDO
        }
        const varName = this.sanitizeVariableName(pdo, index, 'rx');
        if (pdo.type === 'bool') {
            return `    EC_WRITE_BIT(domain1_pd + offset.pdo_slave${index}_rx_${varName}_off, offset.pdo_slave${index}_rx_${varName}_bit, ethercat_shm->shm_slave${index}_rx_${varName});`;
        } else {
            const writeFunc = this.getPDOWriteFunction(pdo.type);
            return `    ${writeFunc}(domain1_pd + offset.pdo_slave${index}_rx_${varName}, ethercat_shm->shm_slave${index}_rx_${varName});`;
        }
    }).filter(line => line !== '').join('\n')
).join('\n')}

    // Send process data
    ecrt_domain_queue(domain1);
    ecrt_master_sync_slave_clocks(master);
    ecrt_master_sync_reference_clock(master);
    ecrt_master_send(master);

    if (last_cycle) {
        printf("Executing final cycle...\\n");
        
        // 将从站切换到预运行状态
        printf("Switching slaves to PREOP state...\\n");
        ecrt_master_deactivate(master);
        
        // 释放EtherCAT主站
        if (master) {
            printf("Releasing master...\\n");
            ecrt_release_master(master);
        }
        
        // 清理共享内存
        cleanup_shm();
        
        printf("Shutdown complete\\n");
        run = 0;  // 这将导致主循环退出
    }
}`;

    return task;
  }

  private static generateMainFunction(config: TemplateConfig): string {
    // Debug: 打印SDO配置数据
    console.log('Generating main function with SDO configuration:');
    // config.slaves.forEach((slave: any, index: number) => {
    //   console.log(`Slave ${index} SDOs:`, JSON.stringify(slave.sdos, null, 2));
    // });

    let mainFunc = `
int main(int argc, char **argv) {
    // Set up signal handler for cleanup
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);

    // Lock memory to prevent paging
    if (mlockall(MCL_CURRENT | MCL_FUTURE) == -1) {
        perror("mlockall failed");
        return -1;
    }

    // Create shared memory
    create_shm();
    
    // Initialize EtherCAT master
    printf("Requesting master...\\n");
    master = ecrt_request_master(MASTER_INDEX);
    if (!master) exit(EXIT_FAILURE);
    
    domain1 = ecrt_master_create_domain(master);
    if (!domain1) exit(EXIT_FAILURE);
 
    // Configure slaves
    printf("Configuring all slaves...\\n");
    
    // 创建从站配置数组
    ec_slave_config_t *slave_configs[${config.slaves.length}];
    
    // 批量获取所有从站配置
    ${config.slaves.map((slave: any, index: number) => {
        console.log(`Generating SDO config for slave ${index}:`, JSON.stringify(slave.sdos, null, 2));
        return `
    printf("Configuring slave ${index}...\\n");
    slave_configs[${index}] = ecrt_master_slave_config(master, slave${index}_POS, slave${index}_VID_PID);
    if (!slave_configs[${index}]) {
        fprintf(stderr, "Failed to get slave${index} configuration!\\n");
        exit(EXIT_FAILURE);
    }
    
    // Configure SDOs for slave ${index}
    printf("Configuring SDOs for slave ${index}...\\n");
    ${(slave.sdos || []).map((sdo: SDOConfig) => {
        console.log(`Processing SDO config:`, JSON.stringify(sdo, null, 2));
        const typeFunc = this.getSDOConfigFunction(sdo.type);
        const dataVarName = `sdo_${index}_${sdo.index.replace('0x', '')}_${sdo.subindex}_data`;
        return `printf("  Configuring SDO ${sdo.index}:${sdo.subindex} (${sdo.name})...\\n");
    if (${typeFunc}(slave_configs[${index}], ${sdo.index}, ${sdo.subindex}, ${dataVarName})) {
        fprintf(stderr, "Failed to configure SDO ${sdo.index}:${sdo.subindex} for slave ${index}\\n");
        exit(EXIT_FAILURE);
    }`
    }).join('\n    ')}
    
    printf("SDO configuration completed for slave ${index}\\n");
`}).join('\n')}
    
    // 保存配置引用
    ${config.slaves.map((slave: any, index: number) => `
    sc_slave${index} = slave_configs[${index}];`).join('\n')}
    
    // 创建PDO配置线程
    pthread_t threads[${config.slaves.length}];
    int config_results[${config.slaves.length}] = {0};
    config_thread_param_t thread_params[${config.slaves.length}];
    
    printf("Starting parallel PDO configuration...\\n");
    
    // 初始化PDO配置线程参数
    ${config.slaves.map((slave: any, index: number) => `
    thread_params[${index}].slave_config = slave_configs[${index}];
    thread_params[${index}].sync_info = slave${index}_syncs;
    thread_params[${index}].result = &config_results[${index}];`).join('\n')}
    
    // 启动PDO配置线程
    for(int i = 0; i < ${config.slaves.length}; i++) {
        if (pthread_create(&threads[i], NULL, config_slave_thread, &thread_params[i])) {
            fprintf(stderr, "Failed to create PDO configuration thread %d\\n", i);
            exit(EXIT_FAILURE);
        }
    }
    
    // 等待所有PDO配置线程完成
    for(int i = 0; i < ${config.slaves.length}; i++) {
        pthread_join(threads[i], NULL);
        if (config_results[i]) {
            fprintf(stderr, "Failed to configure PDOs for slave %d\\n", i);
            exit(EXIT_FAILURE);
        }
    }
    
    printf("Parallel PDO configuration completed\\n");

    // 配置 DC
    printf("Starting parallel DC configuration...\\n");
    
    // 创建DC配置线程数组和参数
    pthread_t dc_threads[${config.slaves.length}];
    int dc_results[${config.slaves.length}] = {0};
    dc_config_thread_param_t dc_params[${config.slaves.length}];
    
    // 初始化DC配置参数
    ${config.slaves.map((slave: any, index: number) => `
    ${typeof slave.dc_config === 'object' ? `
    dc_params[${index}].slave_config = slave_configs[${index}];
    dc_params[${index}].assign_activate = ${slave.dc_config.assign_activate || '0x0300'};
    dc_params[${index}].sync0_cycle = ${slave.dc_config.sync0_cycle === '' ? 'PERIOD_NS' : slave.dc_config.sync0_cycle || 'PERIOD_NS'};
    dc_params[${index}].sync0_shift = ${slave.dc_config.sync0_shift === '' ? 'PERIOD_NS/2' : slave.dc_config.sync0_shift || 'PERIOD_NS/2'};
    dc_params[${index}].sync1_cycle = ${slave.dc_config.sync1_cycle === '' ? '0' : slave.dc_config.sync1_cycle || '0'};
    dc_params[${index}].sync1_shift = ${slave.dc_config.sync1_shift === '' ? '0' : slave.dc_config.sync1_shift || '0'};
    dc_params[${index}].result = &dc_results[${index}];
    
    // 创建DC配置线程
    if (pthread_create(&dc_threads[${index}], NULL, dc_config_thread, &dc_params[${index}])) {
        fprintf(stderr, "Failed to create DC configuration thread for slave %d\\n", ${index});
        exit(EXIT_FAILURE);
    }` : 
    '// No DC configuration needed for this slave'}`).join('\n')}
    
    // 等待所有DC配置线程完成
    ${config.slaves.map((slave: any, index: number) => 
        typeof slave.dc_config === 'object' ? 
        `pthread_join(dc_threads[${index}], NULL);
    if (dc_results[${index}]) {
        fprintf(stderr, "Failed to configure DC for slave %d\\n", ${index});
        exit(EXIT_FAILURE);
    }` : 
        '// Skip waiting for non-DC slave'
    ).join('\n')}
    
    printf("Parallel DC configuration completed\\n");

    if (ecrt_domain_reg_pdo_entry_list(domain1, domain1_regs)) {
        fprintf(stderr, "PDO entry registration failed!\\n");
        exit(EXIT_FAILURE);
    }

    printf("Activating master...\\n");
    if (ecrt_master_activate(master)) {
        exit(EXIT_FAILURE);
    }

    if (!(domain1_pd = ecrt_domain_data(domain1))) {
        exit(EXIT_FAILURE);
    }

    // Set real-time priority
    struct sched_param param = {};
    param.sched_priority = sched_get_priority_max(SCHED_FIFO);
    printf("Using priority %i.\\n", param.sched_priority);
    if (sched_setscheduler(0, SCHED_FIFO, &param) == -1) {
        perror("sched_setscheduler failed");
    }

    printf("Started.\\n");
    printf("Shared memory interface created at %s\\n", ETHERCAT_SHM_FILE);
    
    // 修改后的主循环
    while (run) {
        cyclic_task();
    }

    return EXIT_SUCCESS;
}`;
    return mainFunc;
  }

  private static getSDOConfigFunction(type: string): string {
    switch (type.toLowerCase()) {
        case 'uint8':
            return 'ecrt_slave_config_sdo8';
        case 'uint16':
            return 'ecrt_slave_config_sdo16';
        case 'uint32':
            return 'ecrt_slave_config_sdo32';
        default:
            return 'ecrt_slave_config_sdo32';
    }
  }

  private static generateCStruct(config: any): string {
    const lines: string[] = [];
    
    if (config.slaves && config.slaves.length > 0) {
      // 遍历所有从站
      config.slaves.forEach((slave: any, index: number) => {
        // 处理 RxPDOs
        if (slave.rx_pdos) {
          slave.rx_pdos.forEach((pdo: any) => {
            if (pdo && pdo.index && pdo.name) {  // 添加空值检查
              const varName = this.sanitizeVariableName(pdo, index, 'rx');
              const comment = pdo.comment ? ` /* ${pdo.comment} */` : '';
              lines.push(`    int ${varName};${comment}`);
            }
          });
        }

        // 处理 TxPDOs
        if (slave.tx_pdos) {
          slave.tx_pdos.forEach((pdo: any) => {
            if (pdo && pdo.index && pdo.name) {  // 添加空值检查
              const varName = this.sanitizeVariableName(pdo, index, 'tx');
              const comment = pdo.comment ? ` /* ${pdo.comment} */` : '';
              lines.push(`    int ${varName};${comment}`);
            }
          });
        }
      });
    }

    return lines.join('\n');
  }

  public static generateProgramServiceScript(
    config: TemplateConfig, 
    middleLayerPath: string, 
    programPath: string, 
    shmFileName: string,
    middlewareServiceName: string,
    programCpu: string = "2"
  ): string {
    const workingDir = path.dirname(programPath);
    console.log('programPath', programPath);
    console.log('shmFileName', shmFileName);
    console.log('middlewareServiceName', middlewareServiceName);
    console.log('programCpu', programCpu);
    return `
[Unit]
StartLimitIntervalSec=0
StartLimitBurst=0
Description=EtherCAT Control Service
After=network.target
[Service]
Type=simple
WorkingDirectory=${workingDir}
ExecStartPre=/bin/bash -c 'echo 1 > /proc/sys/kernel/printk && echo 1 > /proc/sys/vm/overcommit_memory'
ExecStart=/bin/bash -c 'taskset -c ${programCpu} ${programPath}'
#TimeoutStopSec=60
#ExecStop=/bin/bash -c 'pkill -INT  "${path.basename(programPath)}"'
Restart=always
RestartSec=5
ExecStopPost=/bin/bash -c 'systemctl stop ${middlewareServiceName}'
[Install]
WantedBy=multi-user.target
`;
  }

  public static generateMiddlewareScript(
    config: TemplateConfig, 
    middleLayerPath: string, 
    programPath: string, 
    shmFileName: string,
    middlewareCpu: string = "3"  // 新增参数，默认值为"3" 
  ): string {
    const workingDir = path.dirname(programPath);
    return `[Unit]
StartLimitIntervalSec=0
StartLimitBurst=0
Description=EtherCAT Middleware Service
After=network.target
[Service]
Type=simple
WorkingDirectory=${workingDir}
ExecStartPre=/bin/bash -c 'echo 1 > /proc/sys/kernel/printk && echo 1 > /proc/sys/vm/overcommit_memory'
ExecStart=/bin/bash -c 'taskset -c ${middlewareCpu} chrt -f 99 ${middleLayerPath}'
#ExecStop=/bin/bash -c 'pkill -INT  "${path.basename(middleLayerPath)}"'
Restart=always
RestartSec=5
#Environment=ETHERCAT_MASTER=${config.masterIndex}

[Install]
WantedBy=multi-user.target
`;
  }

  public static generateBindIrqScript(cpuCore: number = 3): string {
    return `#!/bin/bash

# CPU核心编号，默认为${cpuCore}
CPU_CORE=${cpuCore}

# 获取所有网络设备（排除特定设备）
get_network_devices() {
    ip -br link | awk '$1 !~ /^(lo|virbr|docker|veth|br-|tun|tap|bond|team)/ && $1 !~ /^wl/ {print $1}'
}

# 将十六进制掩码转换为CPU列表
hex_to_cpulist() {
    local hex=$1
    # 确保输入是大写的十六进制
    hex=$(echo "$hex" | tr '[:lower:]' '[:upper:]')
    # 转换十六进制为二进制，并确保至少8位
    local bin=$(echo "ibase=16;obase=2;$hex" | bc | awk '{printf "%08d\\n", $0}')
    local cpus=""
    
    # 从右到左遍历二进制位
    for (( i=0; i<\${#bin}; i++ )); do
        if [ "\${bin:\${#bin}-i-1:1}" = "1" ]; then
            if [ -z "$cpus" ]; then
                cpus="$i"
            else
                cpus="$cpus,$i"
            fi
        fi
    done
    
    if [ -z "$cpus" ]; then
        echo "没有绑定CPU"
    else
        echo "CPU$cpus"
    fi
}

# 列出所有网卡相关的中断
list_network_irqs() {
    echo "发现以下网卡相关中断："
    echo "IRQ    当前绑定到    计数    类型"
    echo "----------------------------------------"
    
    # 获取所有网络设备
    ETHERNET_DEVS=($(get_network_devices))
    
    # 构建grep模式
    GREP_PATTERN=""
    for dev in "\${ETHERNET_DEVS[@]}"; do
        if [ -z "$GREP_PATTERN" ]; then
            GREP_PATTERN="\${dev}.*tx\\|\${dev}.*rx"
        else
            GREP_PATTERN="\${GREP_PATTERN}\\|\${dev}.*tx\\|\${dev}.*rx"
        fi
    done
    
    # 使用两次grep，分别匹配网卡中断和gmac
    (grep -E "$GREP_PATTERN" /proc/interrupts; grep "gmac[0-9]" /proc/interrupts) | while read -r line; do
        irq=$(echo "$line" | awk '{print $1}' | tr -d ':')
        count=$(echo "$line" | awk '{print $2}')
        type=$(echo "$line" | grep -oE "[a-zA-Z0-9]+-[t|r]x-[0-9]+" || echo "$line" | grep -oE "gmac[0-9]+")
        if [ ! -z "$irq" ]; then
            affinity=$(cat /proc/irq/$irq/smp_affinity)
            cpu_list=$(hex_to_cpulist $affinity)
            echo "$irq    $cpu_list    $count    $type"
        fi
    done
}

# 列出当前中断分配情况
list_network_irqs

echo "开始将中断绑定到 CPU$CPU_CORE..."

# 计算smp_affinity值 (2的CPU编号次方)
AFFINITY_VALUE=$(echo "obase=16;$((1 << $CPU_CORE))" | bc)

# 获取所有网络设备
ETHERNET_DEVS=($(get_network_devices))

# 构建grep模式
GREP_PATTERN=""
for dev in "\${ETHERNET_DEVS[@]}"; do
    if [ -z "$GREP_PATTERN" ]; then
        GREP_PATTERN="\${dev}.*tx\\|\${dev}.*rx"
    else
        GREP_PATTERN="\${GREP_PATTERN}\\|\${dev}.*tx\\|\${dev}.*rx"
    fi
done

# 使用两次grep，分别匹配网卡中断和gmac
(grep -E "$GREP_PATTERN" /proc/interrupts; grep "gmac[0-9]" /proc/interrupts) | while read -r line; do
    irq=$(echo "$line" | awk '{print $1}' | tr -d ':')
    type=$(echo "$line" | grep -oE "[a-zA-Z0-9]+-[t|r]x-[0-9]+" || echo "$line" | grep -oE "gmac[0-9]+")
    if [ ! -z "$irq" ]; then
        echo "正在将 $type (IRQ: $irq) 绑定到 CPU$CPU_CORE..."
        echo $AFFINITY_VALUE > /proc/irq/$irq/smp_affinity
    fi
done

echo "绑定完成"
`;
  }

  public static generateProgramServiceScriptWithEnv(): string {
    return `[Unit]
StartLimitIntervalSec=0
StartLimitBurst=0
Description=EtherCAT Program Service %i
After=network.target

[Service]
Type=simple
# 从 env 加载参数，环境文件中应定义：
#   FULL_PATH（路径变量，与其他服务一致）
#   PROGRAMCORE（指定 program 使用的 CPU 核心）
#   OPTION（0: 禁用服务, 1: 启用服务）
#   AC_LOSS_RECOVER（0: 不恢复, 1: 恢复）
#   FORCE_ENABLE（0: 不强制启用, 1: 强制启用）
EnvironmentFile=/app/ethercat-web-ui/backend/env/%i.env
ExecStartPre=/bin/bash -c 'if [ "$FORCE_ENABLE" -eq 1 ]; then echo "[强制模式] 无视AC_LOSS_RECOVER，服务必须启动"; exit 0; elif [ "$AC_LOSS_RECOVER" -eq 1 ]; then echo "[断电恢复模式] 服务允许启动"; exit 0; else echo "[禁用状态] 服务启动被阻止"; systemctl stop ethercat_program@$\{PROGRAM_NAME\}; fi'
ExecStart=/bin/bash -c  'cd $FULL_PATH && taskset -c $PROGRAMCORE $FULL_PATH/$PROGRAM_NAME'
ExecStartPost=/bin/bash -c 'sed -i "s/^AC_LOSS_RECOVER=.*/AC_LOSS_RECOVER=1/" /app/ethercat-web-ui/backend/env/$\{PROGRAM_NAME\}.env'
ExecStopPost=/bin/bash -c  'systemctl stop ethercat_middleware@$\{PROGRAM_NAME\}'
Restart=always
RestartSec=3

StandardOutput=append:/dev/null
StandardError=append:/dev/null
[Install]
WantedBy=multi-user.target
`;
  }

  public static generateMiddlewareServiceScriptWithEnv(): string {
    return `[Unit]
StartLimitIntervalSec=0
StartLimitBurst=0
Description=EtherCAT Middleware Service %i
After=network.target

[Service]
Type=simple
# 从 env 加载参数，环境文件中应定义：
#   FULL_PATH（路径变量，与其他服务一致）
#   MIDDLEWARECORE（指定 middleware 使用的 CPU 核心）
#   OPTION（0: 禁用服务, 1: 启用服务）
EnvironmentFile=/app/ethercat-web-ui/backend/env/%i.env
ExecStartPre=/bin/bash -c 'echo 1 > /proc/sys/kernel/printk && echo 1 > /proc/sys/vm/overcommit_memory'
ExecStart=/bin/bash -c 'cd $FULL_PATH && taskset -c $MIDDLEWARECORE chrt -f 99 $FULL_PATH/middleware'

Restart=always
RestartSec=5

[Install]
WantedBy=multi-user.target
`;
  }

  private static filterZeroIndexPDOs(template: string): string {
    let filteredTemplate = template;
    // 用于存储需要过滤的PDO标识符
    const zeroPdoIdentifiers: Set<string> = new Set();
    
    // 辅助函数：从变量名中提取PDO索引并判断是否全为0
    const isZeroIndex = (line: string): boolean => {
      const match = line.match(/[rt]x_0x([0-9a-fA-F]+)/);
      if (match) {
        const pdoIndex = match[1];
        const isZero = /^0+$/.test(pdoIndex);
        if (isZero) {
          // 提取完整的PDO标识符并清理特殊字符
          const identifierMatch = line.match(/pdo_slave\d+_[rt]x_[^,\s;{}()]+/);
          if (identifierMatch) {
            // 清理标识符，只保留字母、数字和下划线
            const cleanIdentifier = identifierMatch[0].replace(/[^a-zA-Z0-9_]/g, '');
            zeroPdoIdentifiers.add(cleanIdentifier);
            console.log(`找到零索引PDO标识符: ${cleanIdentifier}`);
          }
        }
        // console.log(`检查PDO索引: 0x${pdoIndex}, 是否全为0: ${isZero}`);
        return isZero;
      }
      return false;
    };
  
    // 过滤函数：判断是否保留该行
    const shouldKeepLine = (line: string): boolean => {
      const trimmedLine = line.trim();
      if (!trimmedLine || trimmedLine === '{' || trimmedLine === '}') {
        return true;
      }
      return !isZeroIndex(line);
    };
  
    // 1. 过滤 ethercat_shm_t 结构并收集零索引PDO标识符
    console.log('\n开始处理 ethercat_shm_t 结构...');
    const shmStructRegex = /typedef struct \{[\s\S]*?\} ethercat_shm_t;/;
    const shmMatch = template.match(shmStructRegex);
    if (shmMatch) {
      let shmStruct = shmMatch[0];
      const originalLines = shmStruct.split('\n');
      const filteredShmLines = originalLines.filter(line => {
        const keep = shouldKeepLine(line);
        if (!keep) {
          console.log(`[ethercat_shm_t] 移除行: ${line.trim()}`);
        }
        return keep;
      });
      console.log(`ethercat_shm_t: 原始行数 ${originalLines.length}, 过滤后行数 ${filteredShmLines.length}`);
      filteredTemplate = filteredTemplate.replace(shmStructRegex, filteredShmLines.join('\n'));
    }
  
    // 2. 过滤 offset 结构
    console.log('\n开始处理 offset 结构...');
    const offsetStructRegex = /static struct \{[\s\S]*?\} offset;/;
    const offsetMatch = template.match(offsetStructRegex);
    if (offsetMatch) {
      let offsetStruct = offsetMatch[0];
      const originalLines = offsetStruct.split('\n');
      const filteredOffsetLines = originalLines.filter(line => {
        const keep = shouldKeepLine(line);
        if (!keep) {
          console.log(`[offset] 移除行: ${line.trim()}`);
        }
        return keep;
      });
      console.log(`offset: 原始行数 ${originalLines.length}, 过滤后行数 ${filteredOffsetLines.length}`);
      filteredTemplate = filteredTemplate.replace(offsetStructRegex, filteredOffsetLines.join('\n'));
    }
  
    // 3. 过滤 domain1_regs 数组
    console.log('\n开始处理 domain1_regs 数组...');
    const domainRegsRegex = /const static ec_pdo_entry_reg_t domain1_regs\[\] = \{[\s\S]*?\};/;
    const domainMatch = template.match(domainRegsRegex);
    if (domainMatch) {
      let domainRegs = domainMatch[0];
      const originalLines = domainRegs.split('\n');
      const filteredDomainLines = originalLines.filter(line => {
        const match = line.match(/0x([0-9a-fA-F]+)/);
        if (!match) {
          return true;
        }
        const pdoIndex = match[1];
        const isZero = /^0+$/.test(pdoIndex);
        if (isZero) {
          console.log(`[domain1_regs] 移除行: ${line.trim()}`);
        }
        return !isZero;
      });
      console.log(`domain1_regs: 原始行数 ${originalLines.length}, 过滤后行数 ${filteredDomainLines.length}`);
      filteredTemplate = filteredTemplate.replace(domainRegsRegex, filteredDomainLines.join('\n'));
    }
  
    // 修改过滤 EC_WRITE 相关代码的部分
    console.log('\n开始处理 EC_WRITE 相关代码...');
    if (zeroPdoIdentifiers.size > 0) {
      console.log('需要过滤的PDO标识符:', Array.from(zeroPdoIdentifiers));
      
      const lines = filteredTemplate.split('\n');
      const filteredLines = lines.filter(line => {
        const trimmedLine = line.trim();
        if (!trimmedLine.startsWith('EC_WRITE')) {
          return true;  // 不是 EC_WRITE 行，保留
        }

        // 检查是否包含任何需要过滤的标识符
        let shouldKeepLine = true;
        for (const identifier of zeroPdoIdentifiers) {
          if (line.includes(identifier)) {
            console.log(`[EC_WRITE] 发现需要过滤的标识符 "${identifier}" 在行: ${line.trim()}`);
            shouldKeepLine = false;
            break;  // 找到一个匹配就可以确定要过滤这行了
          }
        }

        if (!shouldKeepLine) {
          console.log(`[EC_WRITE] 移除行: ${line.trim()}`);
        }
        return shouldKeepLine;
      });

      // 输出过滤统计
      const removedLines = lines.length - filteredLines.length;
      console.log(`\n过滤统计:`);
      console.log(`- 原始行数: ${lines.length}`);
      console.log(`- 过滤后行数: ${filteredLines.length}`);
      console.log(`- 移除的行数: ${removedLines}`);

      filteredTemplate = filteredLines.join('\n');
    }
  
    // 添加总结信息
    console.log('\n过滤完成！');
    console.log(`总共过滤掉 ${zeroPdoIdentifiers.size} 个零索引PDO标识符`);
    console.log('请检查生成的代码中是否已正确移除所有索引为0的PDO项。');
  
    return filteredTemplate;
  }
} 
