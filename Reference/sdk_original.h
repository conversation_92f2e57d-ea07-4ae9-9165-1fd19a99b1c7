#ifndef SDK_H
#define SDK_H

#include <stdint.h> // For int32_t, uint16_t, uint8_t
#include <stddef.h> // For size_t (though not directly used in API, good include for C)

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief Initializes the EtherCAT SDK.
 * This function sets up the EtherCAT master, configures slaves based on the
 * provided JSON configuration, and initializes the shared memory segment.
 *
 * @return 0 on success, a negative error code on failure.
 */
int sdk_init();

/**
 * @brief Connects to an existing shared memory segment using a default name.
 * This function is intended for client applications that only need to read from
 * or write to the shared memory created by a process that called sdk_init().
 * It does not initialize the EtherCAT master or any related resources.
 *
 * @return 0 on success, a negative error code on failure.
 */
int sdk_connect();

/**
 * @brief Starts the EtherCAT cyclic communication task.
 * This task runs in a separate thread and handles periodic process data exchange.
 *
 * @return 0 on success, a negative error code if the task cannot be started.
 */
int sdk_start_cyclic_task();

/**
 * @brief Stops the EtherCAT cyclic communication task.
 * Signals the cyclic task to terminate and waits for it to exit gracefully.
 */
void sdk_stop_cyclic_task();

/**
 * @brief Cleans up all resources used by the SDK.
 * This includes deactivating and releasing the EtherCAT master, and unmapping
 * and unlinking the shared memory.
 */
void sdk_cleanup();

/**
 * @brief Writes a 32-bit integer value to a specific object of a slave
 *        in the internally managed shared memory.
 * The mapping from (slave_number, index, subindex) to the actual memory
 * location within the shared memory is determined by the SDK's configuration
 * (derived from the JSON file). This function is intended for writing
 * RxPDO data or control parameters.
 *
 * @param slave_number Logical identifier for the slave (e.g., 0 for the first slave).
 * @param index The object dictionary index (e.g., 0x6060).
 * @param subindex The object dictionary subindex (e.g., 0).
 * @param value The int32_t value to write.
 * @return 0 on success, a negative error code on failure (e.g., invalid parameters,
 *         object not found, or SHM not initialized).
 */
int sdk_write(uint16_t slave_number, uint16_t index, uint8_t subindex, int32_t value);

/**
 * @brief Reads a 32-bit integer value from a specific object of a slave
 *        in the internally managed shared memory.
 * The mapping from (slave_number, index, subindex) to the actual memory
 * location is determined by the SDK's configuration. This function is
 * intended for reading TxPDO data or status parameters.
 *
 * @param slave_number Logical identifier for the slave.
 * @param index The object dictionary index.
 * @param subindex The object dictionary subindex.
 * @param value_ptr Pointer to an int32_t variable where the read value will be stored.
 * @return 0 on success, a negative error code on failure.
 */
int sdk_read(uint16_t slave_number, uint16_t index, uint8_t subindex, int32_t* value_ptr);



/**
 * @brief Retrieves the status of a specific EtherCAT slave.
 *
 * @param slave_number Logical identifier for the slave.
 * @param online_status Pointer to store the online status (1 if online, 0 if offline).
 * @param op_status Pointer to store the operational status (1 if operational, 0 otherwise).
 * @param al_state Pointer to store the AL (Application Layer) state code.
 * @return 0 on success, a negative error code if slave not found or status unavailable.
 */
int sdk_get_slave_status(uint16_t slave_number, int32_t* online_status, int32_t* op_status, int32_t* al_state);

/**
 * @brief Optional: Sets the CPU core affinity for the cyclic task.
 * Must be called before sdk_start_cyclic_task().
 *
 * @param core_id The ID of the CPU core to pin the task to.
 * @return 0 on success, a negative error code on failure.
 */
// int sdk_set_core_affinity(int core_id); // Implementation is platform-specific (pthreads)

#ifdef __cplusplus
}
#endif

#endif // SDK_H 