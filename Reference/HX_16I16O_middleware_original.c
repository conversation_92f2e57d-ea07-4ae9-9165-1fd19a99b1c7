
#include <errno.h>
#include <signal.h>
#include <stdio.h>
#include <string.h>
#include <sys/resource.h>
#include <sys/time.h>
#include <sys/types.h>
#include <unistd.h>
#include <sys/mman.h>
#include <fcntl.h>
#include <sched.h>
#include <sys/stat.h>
#include <sys/shm.h>
#include <time.h>
#include <pthread.h>  // 添加pthread头文件

#include "ecrt.h"

// Forward declarations
void cleanup_shm(void);
void signal_handler(int sig);

// 定义PDO配置线程的参数结构
typedef struct {
    ec_slave_config_t *slave_config;
    ec_sync_info_t *sync_info;
    int *result;
} config_thread_param_t;

// PDO配置线程函数
void* config_slave_thread(void *arg) {
    config_thread_param_t *param = (config_thread_param_t *)arg;
    *(param->result) = ecrt_slave_config_pdos(param->slave_config, EC_END, param->sync_info);
    return NULL;
}

// 定义DC配置线程的参数结构
typedef struct {
    ec_slave_config_t *slave_config;
    uint16_t assign_activate;
    uint32_t sync0_cycle;
    uint32_t sync0_shift;
    uint32_t sync1_cycle;
    uint32_t sync1_shift;
    int *result;
} dc_config_thread_param_t;

// DC配置线程函数
void* dc_config_thread(void *arg) {
    dc_config_thread_param_t *param = (dc_config_thread_param_t *)arg;
    int ret = ecrt_slave_config_dc(
        param->slave_config,
        param->assign_activate,
        param->sync0_cycle,
        param->sync0_shift,
        param->sync1_cycle,
        param->sync1_shift
    );
    *(param->result) = ret;
    return NULL;
}

// Global control flags
static int run = 1;  // 控制主循环的标志
static int last_cycle = 0;  // 标记最后一个循环

// Signal handler implementation
void signal_handler(int sig) {
    printf("\nSignal %d received, will exit after next cycle...\n", sig);
    last_cycle = 1;  // 设置最后一个循环标志
}

/* Time definitions */
#define NSEC_PER_SEC (1000000000L)
#define TIMESPEC2NS(T) ((uint64_t) (T).tv_sec * NSEC_PER_SEC + (T).tv_nsec)
#define CLOCK_TO_USE CLOCK_MONOTONIC

/* Shared memory configuration */
#define ETHERCAT_SHM_FILE "0pajxolm9unxms3_HX_16I16O_shm"
#define ETHERCAT_SHM_SIZE (sizeof(ethercat_shm_t))

/* Shared memory structure */
typedef struct {
    int shm_slave0_online_status; /* 从站0在线状态 */
    int shm_slave0_operational_status; /* 从站0运行状态 */
    int shm_slave0_al_state; /* 从站0AL状态 */

    int shm_slave0_rx_shm_slave0_rx_0x00007100_outbyte0;
    int shm_slave0_rx_shm_slave0_rx_0x00007100_outbyte1;
    int shm_slave0_tx_shm_slave0_tx_0x00006000_inbyte0;
    int shm_slave0_tx_shm_slave0_tx_0x00006000_inbyte1;
    int shm_slave0_tx_shm_slave0_tx_0x00008002_module_state;
    int shm_slave0_tx_shm_slave0_tx_0x00008003_module_err_num;
    int shm_slave0_tx_shm_slave0_tx_0x00008102_module_state;
    int shm_slave0_tx_shm_slave0_tx_0x00008103_module_err_num;
} ethercat_shm_t;

static ethercat_shm_t *ethercat_shm = NULL;

/* Application Parameters */
#define TASK_FREQUENCY 1000 /*Hz*/
#define PERIOD_NS (NSEC_PER_SEC / TASK_FREQUENCY)

/* 状态更新参数 */
#define STATUS_UPDATE_INTERVAL_MS 1000 /* 固定的状态更新间隔，毫秒 */
/* 根据任务频率自动计算需要多少个周期才能达到指定的时间间隔 */
#define STATUS_UPDATE_PERIOD ((TASK_FREQUENCY * STATUS_UPDATE_INTERVAL_MS) / 1000)

/* EtherCAT configurations */
#define MASTER_INDEX 1

static ec_master_t *master = NULL;
static ec_master_state_t master_state = {};
static ec_domain_t *domain1 = NULL;
static ec_domain_state_t domain1_state = {};
static ec_slave_config_t *sc_slave0 = NULL;
static ec_slave_config_state_t sc_slave0_state = {};
static uint8_t *domain1_pd = NULL;

#define slave0_POS 0,0
#define slave0_VID_PID 0x00000099,0x00020310

/* PDO mapping */

/* SDO data definitions */
// SDO data for slave 0
static uint32_t sdo_0_8001_1_data = 0x00000181;  // Module Config 1 (0x8001:1)
static uint8_t sdo_0_6001_1_data = 0x06;  // Module Config 1 (0x6001:1)
static uint8_t sdo_0_6001_2_data = 0x06;  // Module Config 2 (0x6001:2)
static uint8_t sdo_0_6001_3_data = 0x06;  // Module Config 3 (0x6001:3)
static uint8_t sdo_0_6001_4_data = 0x06;  // Module Config 4 (0x6001:4)
static uint32_t sdo_0_8101_1_data = 0x00000372;  // Module Config 1 (0x8101:1)
static uint8_t sdo_0_7102_1_data = 0xff;  // Module Config 1 (0x7102:1)
static uint8_t sdo_0_7102_2_data = 0xff;  // Module Config 2 (0x7102:2)

static struct {
    unsigned int pdo_slave0_rx_shm_slave0_rx_0x00007100_outbyte0;
    unsigned int pdo_slave0_rx_shm_slave0_rx_0x00007100_outbyte1;
    unsigned int pdo_slave0_tx_shm_slave0_tx_0x00006000_inbyte0;
    unsigned int pdo_slave0_tx_shm_slave0_tx_0x00006000_inbyte1;
    unsigned int pdo_slave0_tx_shm_slave0_tx_0x00008002_module_state;
    unsigned int pdo_slave0_tx_shm_slave0_tx_0x00008003_module_err_num;
    unsigned int pdo_slave0_tx_shm_slave0_tx_0x00008102_module_state;
    unsigned int pdo_slave0_tx_shm_slave0_tx_0x00008103_module_err_num;
} offset;

const static ec_pdo_entry_reg_t domain1_regs[] = {
    {slave0_POS, slave0_VID_PID, 0x00007100, 1, &offset.pdo_slave0_rx_shm_slave0_rx_0x00007100_outbyte0},
    {slave0_POS, slave0_VID_PID, 0x00007100, 2, &offset.pdo_slave0_rx_shm_slave0_rx_0x00007100_outbyte1},
    {slave0_POS, slave0_VID_PID, 0x00006000, 1, &offset.pdo_slave0_tx_shm_slave0_tx_0x00006000_inbyte0},
    {slave0_POS, slave0_VID_PID, 0x00006000, 2, &offset.pdo_slave0_tx_shm_slave0_tx_0x00006000_inbyte1},
    {slave0_POS, slave0_VID_PID, 0x00008002, 0, &offset.pdo_slave0_tx_shm_slave0_tx_0x00008002_module_state},
    {slave0_POS, slave0_VID_PID, 0x00008003, 0, &offset.pdo_slave0_tx_shm_slave0_tx_0x00008003_module_err_num},
    {slave0_POS, slave0_VID_PID, 0x00008102, 0, &offset.pdo_slave0_tx_shm_slave0_tx_0x00008102_module_state},
    {slave0_POS, slave0_VID_PID, 0x00008103, 0, &offset.pdo_slave0_tx_shm_slave0_tx_0x00008103_module_err_num},
    {}
};


static ec_pdo_entry_info_t slave0_pdo_entries[] = {
    {0x00007100, 1, 8},  /* OutByte0 */
    {0x00007100, 2, 8},  /* OutByte1 */
    {0x00006000, 1, 8},  /* InByte0 */
    {0x00006000, 2, 8},  /* InByte1 */
    {0x00008002, 0, 16},  /* Module State */
    {0x00008003, 0, 32},  /* Module Err Num */
    {0x00008102, 0, 16},  /* Module State */
    {0x00008103, 0, 32},  /* Module Err Num */
};

static ec_pdo_info_t slave0_pdos[] = {
    {0x00001601, 2, slave0_pdo_entries + 0},  /* RxPDO */
    {0x00001a00, 4, slave0_pdo_entries + 2},  /* TxPDO */
    {0x00001a01, 2, slave0_pdo_entries + 6},  /* TxPDO */
};

static ec_sync_info_t slave0_syncs[] = {
    {0, EC_DIR_OUTPUT, 0, NULL, EC_WD_DISABLE},
    {1, EC_DIR_INPUT, 0, NULL, EC_WD_DISABLE},
    {2, EC_DIR_OUTPUT, 1, slave0_pdos + 0, EC_WD_ENABLE},
    {3, EC_DIR_INPUT, 2, slave0_pdos + 1, EC_WD_DISABLE},
    {0xff}
};

void create_shm() {
    // Create and open shared memory
    int fd = shm_open(ETHERCAT_SHM_FILE, O_CREAT | O_RDWR, 0666);
    if (fd < 0) {
        perror("shm_open failed");
        exit(EXIT_FAILURE);
    }

    // Set the size of shared memory
    if (ftruncate(fd, ETHERCAT_SHM_SIZE) < 0) {
        perror("ftruncate failed");
        exit(EXIT_FAILURE);
    }

    // Map shared memory
    ethercat_shm = (ethercat_shm_t *)mmap(NULL, ETHERCAT_SHM_SIZE, 
                                         PROT_READ | PROT_WRITE, 
                                         MAP_SHARED, fd, 0);
    if (ethercat_shm == MAP_FAILED) {
        perror("mmap failed");
        exit(EXIT_FAILURE);
    }

    // Initialize shared memory to zero
    memset(ethercat_shm, 0, ETHERCAT_SHM_SIZE);
    
    // Close file descriptor (mapping remains valid)
    close(fd);
}

void cleanup_shm() {
    if (ethercat_shm != NULL) {
        munmap(ethercat_shm, ETHERCAT_SHM_SIZE);
        shm_unlink(ETHERCAT_SHM_FILE);
    }
}

void cyclic_task(void)
{
    static int cycle_counter = 0;
    static struct timespec wakeupTime;
    static int initialized = 0;

    if (!initialized) {
        clock_gettime(CLOCK_TO_USE, &wakeupTime);
        initialized = 1;
    }

    wakeupTime.tv_nsec += PERIOD_NS;
    while (wakeupTime.tv_nsec >= NSEC_PER_SEC) {
        wakeupTime.tv_nsec -= NSEC_PER_SEC;
        wakeupTime.tv_sec++;
    }

    clock_nanosleep(CLOCK_TO_USE, TIMER_ABSTIME, &wakeupTime, NULL);

    ecrt_master_application_time(master, TIMESPEC2NS(wakeupTime));

    ecrt_master_receive(master);
    
    // 添加错误检查
    if (ecrt_master_state(master, &master_state)) {
        run = 0;  // 如果出现错误，触发程序退出
        fprintf(stderr, "Failed to get master state.\n");
        return;
    }
    
    ecrt_domain_process(domain1);

    if (cycle_counter) {
        cycle_counter--;
    } else {
        cycle_counter = STATUS_UPDATE_PERIOD;
        
          ecrt_slave_config_state(sc_slave0, &sc_slave0_state);
          // printf("c function slave %d: online=%d, operational=%d, al_state=0x%02X\n", 
          //     0, 
          //     sc_slave0_state.online, 
          //     sc_slave0_state.operational, 
          //     sc_slave0_state.al_state);
          ethercat_shm->shm_slave0_online_status = sc_slave0_state.online;
          ethercat_shm->shm_slave0_operational_status = sc_slave0_state.operational;
          ethercat_shm->shm_slave0_al_state = sc_slave0_state.al_state;
          // printf("share memory slave %d: online=%d, operational=%d, al_state=0x%02X\n", 
          //     0, 
          //     ethercat_shm->shm_slave0_online_status, 
          //     ethercat_shm->shm_slave0_operational_status, 
          //     ethercat_shm->shm_slave0_al_state);
      
    }

    // 更新从站状态

    // Update shared memory with status
    ethercat_shm->shm_slave0_tx_shm_slave0_tx_0x00006000_inbyte0 = EC_READ_U8(domain1_pd + offset.pdo_slave0_tx_shm_slave0_tx_0x00006000_inbyte0);
    ethercat_shm->shm_slave0_tx_shm_slave0_tx_0x00006000_inbyte1 = EC_READ_U8(domain1_pd + offset.pdo_slave0_tx_shm_slave0_tx_0x00006000_inbyte1);
    ethercat_shm->shm_slave0_tx_shm_slave0_tx_0x00008002_module_state = EC_READ_U16(domain1_pd + offset.pdo_slave0_tx_shm_slave0_tx_0x00008002_module_state);
    ethercat_shm->shm_slave0_tx_shm_slave0_tx_0x00008003_module_err_num = EC_READ_U32(domain1_pd + offset.pdo_slave0_tx_shm_slave0_tx_0x00008003_module_err_num);
    ethercat_shm->shm_slave0_tx_shm_slave0_tx_0x00008102_module_state = EC_READ_U16(domain1_pd + offset.pdo_slave0_tx_shm_slave0_tx_0x00008102_module_state);
    ethercat_shm->shm_slave0_tx_shm_slave0_tx_0x00008103_module_err_num = EC_READ_U32(domain1_pd + offset.pdo_slave0_tx_shm_slave0_tx_0x00008103_module_err_num);

    // Write to EtherCAT
    EC_WRITE_U8(domain1_pd + offset.pdo_slave0_rx_shm_slave0_rx_0x00007100_outbyte0, ethercat_shm->shm_slave0_rx_shm_slave0_rx_0x00007100_outbyte0);
    EC_WRITE_U8(domain1_pd + offset.pdo_slave0_rx_shm_slave0_rx_0x00007100_outbyte1, ethercat_shm->shm_slave0_rx_shm_slave0_rx_0x00007100_outbyte1);

    // Send process data
    ecrt_domain_queue(domain1);
    ecrt_master_sync_slave_clocks(master);
    ecrt_master_sync_reference_clock(master);
    ecrt_master_send(master);

    if (last_cycle) {
        printf("Executing final cycle...\n");
        
        // 将从站切换到预运行状态
        printf("Switching slaves to PREOP state...\n");
        ecrt_master_deactivate(master);
        
        // 释放EtherCAT主站
        if (master) {
            printf("Releasing master...\n");
            ecrt_release_master(master);
        }
        
        // 清理共享内存
        cleanup_shm();
        
        printf("Shutdown complete\n");
        run = 0;  // 这将导致主循环退出
    }
}
int main(int argc, char **argv) {
    // Set up signal handler for cleanup
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);

    // Lock memory to prevent paging
    if (mlockall(MCL_CURRENT | MCL_FUTURE) == -1) {
        perror("mlockall failed");
        return -1;
    }

    // Create shared memory
    create_shm();
    
    // Initialize EtherCAT master
    printf("Requesting master...\n");
    master = ecrt_request_master(MASTER_INDEX);
    if (!master) exit(EXIT_FAILURE);
    
    domain1 = ecrt_master_create_domain(master);
    if (!domain1) exit(EXIT_FAILURE);
 
    // Configure slaves
    printf("Configuring all slaves...\n");
    
    // 创建从站配置数组
    ec_slave_config_t *slave_configs[1];
    
    // 批量获取所有从站配置
    
    printf("Configuring slave 0...\n");
    slave_configs[0] = ecrt_master_slave_config(master, slave0_POS, slave0_VID_PID);
    if (!slave_configs[0]) {
        fprintf(stderr, "Failed to get slave0 configuration!\n");
        exit(EXIT_FAILURE);
    }
    
    // Configure SDOs for slave 0
    printf("Configuring SDOs for slave 0...\n");
    printf("  Configuring SDO 0x8001:1 (Module Config 1)...\n");
    if (ecrt_slave_config_sdo32(slave_configs[0], 0x8001, 1, sdo_0_8001_1_data)) {
        fprintf(stderr, "Failed to configure SDO 0x8001:1 for slave 0\n");
        exit(EXIT_FAILURE);
    }
    printf("  Configuring SDO 0x6001:1 (Module Config 1)...\n");
    if (ecrt_slave_config_sdo8(slave_configs[0], 0x6001, 1, sdo_0_6001_1_data)) {
        fprintf(stderr, "Failed to configure SDO 0x6001:1 for slave 0\n");
        exit(EXIT_FAILURE);
    }
    printf("  Configuring SDO 0x6001:2 (Module Config 2)...\n");
    if (ecrt_slave_config_sdo8(slave_configs[0], 0x6001, 2, sdo_0_6001_2_data)) {
        fprintf(stderr, "Failed to configure SDO 0x6001:2 for slave 0\n");
        exit(EXIT_FAILURE);
    }
    printf("  Configuring SDO 0x6001:3 (Module Config 3)...\n");
    if (ecrt_slave_config_sdo8(slave_configs[0], 0x6001, 3, sdo_0_6001_3_data)) {
        fprintf(stderr, "Failed to configure SDO 0x6001:3 for slave 0\n");
        exit(EXIT_FAILURE);
    }
    printf("  Configuring SDO 0x6001:4 (Module Config 4)...\n");
    if (ecrt_slave_config_sdo8(slave_configs[0], 0x6001, 4, sdo_0_6001_4_data)) {
        fprintf(stderr, "Failed to configure SDO 0x6001:4 for slave 0\n");
        exit(EXIT_FAILURE);
    }
    printf("  Configuring SDO 0x8101:1 (Module Config 1)...\n");
    if (ecrt_slave_config_sdo32(slave_configs[0], 0x8101, 1, sdo_0_8101_1_data)) {
        fprintf(stderr, "Failed to configure SDO 0x8101:1 for slave 0\n");
        exit(EXIT_FAILURE);
    }
    printf("  Configuring SDO 0x7102:1 (Module Config 1)...\n");
    if (ecrt_slave_config_sdo8(slave_configs[0], 0x7102, 1, sdo_0_7102_1_data)) {
        fprintf(stderr, "Failed to configure SDO 0x7102:1 for slave 0\n");
        exit(EXIT_FAILURE);
    }
    printf("  Configuring SDO 0x7102:2 (Module Config 2)...\n");
    if (ecrt_slave_config_sdo8(slave_configs[0], 0x7102, 2, sdo_0_7102_2_data)) {
        fprintf(stderr, "Failed to configure SDO 0x7102:2 for slave 0\n");
        exit(EXIT_FAILURE);
    }
    
    printf("SDO configuration completed for slave 0\n");

    
    // 保存配置引用
    
    sc_slave0 = slave_configs[0];
    
    // 创建PDO配置线程
    pthread_t threads[1];
    int config_results[1] = {0};
    config_thread_param_t thread_params[1];
    
    printf("Starting parallel PDO configuration...\n");
    
    // 初始化PDO配置线程参数
    
    thread_params[0].slave_config = slave_configs[0];
    thread_params[0].sync_info = slave0_syncs;
    thread_params[0].result = &config_results[0];
    
    // 启动PDO配置线程
    for(int i = 0; i < 1; i++) {
        if (pthread_create(&threads[i], NULL, config_slave_thread, &thread_params[i])) {
            fprintf(stderr, "Failed to create PDO configuration thread %d\n", i);
            exit(EXIT_FAILURE);
        }
    }
    
    // 等待所有PDO配置线程完成
    for(int i = 0; i < 1; i++) {
        pthread_join(threads[i], NULL);
        if (config_results[i]) {
            fprintf(stderr, "Failed to configure PDOs for slave %d\n", i);
            exit(EXIT_FAILURE);
        }
    }
    
    printf("Parallel PDO configuration completed\n");

    // 配置 DC
    printf("Starting parallel DC configuration...\n");
    
    // 创建DC配置线程数组和参数
    pthread_t dc_threads[1];
    int dc_results[1] = {0};
    dc_config_thread_param_t dc_params[1];
    
    // 初始化DC配置参数
    
    // No DC configuration needed for this slave
    
    // 等待所有DC配置线程完成
    // Skip waiting for non-DC slave
    
    printf("Parallel DC configuration completed\n");

    if (ecrt_domain_reg_pdo_entry_list(domain1, domain1_regs)) {
        fprintf(stderr, "PDO entry registration failed!\n");
        exit(EXIT_FAILURE);
    }

    printf("Activating master...\n");
    if (ecrt_master_activate(master)) {
        exit(EXIT_FAILURE);
    }

    if (!(domain1_pd = ecrt_domain_data(domain1))) {
        exit(EXIT_FAILURE);
    }

    // Set real-time priority
    struct sched_param param = {};
    param.sched_priority = sched_get_priority_max(SCHED_FIFO);
    printf("Using priority %i.\n", param.sched_priority);
    if (sched_setscheduler(0, SCHED_FIFO, &param) == -1) {
        perror("sched_setscheduler failed");
    }

    printf("Started.\n");
    printf("Shared memory interface created at %s\n", ETHERCAT_SHM_FILE);
    
    // 修改后的主循环
    while (run) {
        cyclic_task();
    }

    return EXIT_SUCCESS;
}