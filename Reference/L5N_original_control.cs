using System;
using System.IO;
using System.IO.MemoryMappedFiles;
using System.Runtime.InteropServices;
using System.Threading;
using System.Threading.Tasks;
using System.Diagnostics;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Linq;
using System.Collections.Concurrent;

namespace EtherCATControl
{
    public class AsyncLogger
    {
        private readonly string _logFilePath;
        private readonly ConcurrentQueue<string> _logQueue;
        private readonly CancellationTokenSource _cts;
        private Task _loggingTask;
        private readonly object _lockObject = new object();

        public AsyncLogger(string programName)
        {
            _logFilePath = Path.Combine("/tmp", $"{programName}.log");
            _logQueue = new ConcurrentQueue<string>();
            _cts = new CancellationTokenSource();
        }

        public void Start()
        {
            _loggingTask = Task.Run(ProcessLogQueue);
            Log($"日志服务启动 - {DateTime.Now}");
        }

        public void Stop()
        {
            _cts.Cancel();
            _loggingTask?.Wait();
            Log($"日志服务停止 - {DateTime.Now}");
        }

        public void Log(string message)
        {
            var logMessage = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}] {message}";
            _logQueue.Enqueue(logMessage);
        }

        private async Task ProcessLogQueue()
        {
            while (!_cts.Token.IsCancellationRequested)
            {
                try
                {
                    if (_logQueue.TryDequeue(out string message))
                    {
                        lock (_lockObject)
                        {
                            File.AppendAllText(_logFilePath, message + Environment.NewLine);
                        }
                    }
                    else
                    {
                        await Task.Delay(10, _cts.Token);
                    }
                }
                catch (Exception ex)
                {
                    var errorMessage = $"[ERROR] 日志处理错误: {ex.Message}";
                    lock (_lockObject)
                    {
                        File.AppendAllText(_logFilePath, errorMessage + Environment.NewLine);
                    }
                }
            }
        }
    }

    // 共享内存结构体
    [StructLayout(LayoutKind.Sequential)]
    public struct EtherCATSharedMemory
    {
            public int shm_slave0_online_status; // 从站0在线状态
        public int shm_slave0_operational_status; // 从站0运行状态
        public int shm_slave0_al_state; // 从站0AL状态

        public int shm_slave0_rx_0x6060_operation_mode; // 操作模式设置
        public int shm_slave0_rx_0x60ff_target_speed; // 目标速度
        public int shm_slave0_rx_0x6040_control_word; // 控制字
        public int shm_slave0_tx_0x6041_status_word; // 状态字
        public int shm_slave0_tx_0x603f_error_code; // 错误代码
        public int shm_slave0_tx_0x606c_actual_velocity; // 实际反馈速度
        public int shm_slave0_tx_0x503f_customize_error_code; // 厂商错误码

    }

    // IO模块共享内存结构体
    [StructLayout(LayoutKind.Sequential)]
    public struct IOModuleSharedMemory
    {
        public int shm_slave0_online_status; // 从站0在线状态
        public int shm_slave0_operational_status; // 从站0运行状态
        public int shm_slave0_al_state; // 从站0AL状态

        public int shm_slave0_rx_0x00007100_outbyte0;
        public int shm_slave0_rx_0x00007100_outbyte1;
        public int shm_slave0_tx_0x00006000_inbyte0;
        public int shm_slave0_tx_0x00006000_inbyte1;
        public int shm_slave0_tx_0x00008002_module_state;
        public int shm_slave0_tx_0x00008003_module_err_num;
        public int shm_slave0_tx_0x00008102_module_state;
        public int shm_slave0_tx_0x00008103_module_err_num;
    }

    // Linux RT 相关定义
    [StructLayout(LayoutKind.Sequential)]
    public struct sched_param
    {
        public int sched_priority;
    }

    public class EtherCATController : IDisposable
    {
        private readonly MemoryMappedFile _memoryMappedFile;
        private readonly MemoryMappedViewAccessor _viewAccessor;
        private EtherCATSharedMemory _sharedMemory;
        private readonly AsyncLogger _logger;
        private const int ENABLE_TIMEOUT_MS = 10000; // 使能超时时间

        // P/Invoke 定义
        [DllImport("libc", SetLastError = true)]
        private static extern int sched_setscheduler(int pid, int policy, ref sched_param param);
        
        [DllImport("libc", SetLastError = true)]
        private static extern int sched_get_priority_max(int policy);

        [DllImport("libc")]
        private static extern int getpid();

        public EtherCATController(string EtherCATSharedMemoryFilePath, AsyncLogger logger)
        {
            _memoryMappedFile = MemoryMappedFile.CreateFromFile(EtherCATSharedMemoryFilePath, FileMode.Open);
            _viewAccessor = _memoryMappedFile.CreateViewAccessor();
            _sharedMemory = new EtherCATSharedMemory();
            _logger = logger;
            _logger.Log($"EtherCAT控制器初始化 - 共享内存文件: {EtherCATSharedMemoryFilePath}");
        }

        private void UpdateSharedMemory()
        {
            _viewAccessor.Read(0, out _sharedMemory);
        }

        private void WriteSharedMemory()
        {
            _viewAccessor.Write(0, ref _sharedMemory);
        }

        // 获取状态字
        private int GetStatusWord(int slaveIndex)
        {
            UpdateSharedMemory();
            return _sharedMemory.shm_slave0_tx_0x6041_status_word;
        }

        // 设置控制字
        private void SetControlWord(int slaveIndex, int value)
        {
            UpdateSharedMemory();
            _sharedMemory.shm_slave0_rx_0x6040_control_word = value;
            WriteSharedMemory();
        }

        // 设置操作模式
        private void SetOperationMode(int slaveIndex, int value)
        {
            UpdateSharedMemory();
            _sharedMemory.shm_slave0_rx_0x6060_operation_mode = value;
            WriteSharedMemory();
        }

        // 获取在线状态
        private bool GetSlaveOnlineStatus(int slaveIndex)
        {
            UpdateSharedMemory();
            return _sharedMemory.shm_slave0_online_status == 1;
        }

        // 获取运行状态
        private bool GetSlaveOperationalStatus(int slaveIndex)
        {
            UpdateSharedMemory();
            return _sharedMemory.shm_slave0_operational_status == 1;
        }

        // 获取AL状态
        private int GetSlaveALState(int slaveIndex)
        {
            UpdateSharedMemory();
            return _sharedMemory.shm_slave0_al_state;
        }

        private bool CheckStatusWord(ushort expectedStatus)
        {
            UpdateSharedMemory();
            var currentStatus = _sharedMemory.shm_slave0_tx_0x6041_status_word & 0x0FFF;
            _logger.Log($"状态字检查 - 当前: 0x{currentStatus:X4}, 期望: 0x{expectedStatus:X4}");
            return currentStatus == expectedStatus;
        }

        private bool WaitForStatus(int expectedStatus, int timeoutMs = 1000)
        {
            int elapsed = 0;
            _logger.Log($"等待状态 0x{expectedStatus:X4}, 超时时间: {timeoutMs}ms");
            
            while (elapsed < timeoutMs)
            {
                UpdateSharedMemory();
                int currentStatus = _sharedMemory.shm_slave0_tx_0x6041_status_word & 0x0FFF;
                _logger.Log($"当前状态: 0x{currentStatus:X4}");
                
                if (currentStatus == expectedStatus)
                {
                    _logger.Log("状态匹配成功");
                    return true;
                }
                Thread.Sleep(10);
                elapsed += 10;
            }
            
            _logger.Log($"等待状态超时");
            return false;
        }

        public bool WaitForServoReady(int slaveIndex, CancellationToken cancellationToken = default)
        {
            Console.WriteLine($"等待从站{slaveIndex}伺服就绪...");
            int elapsed = 0;
            
            while (!cancellationToken.IsCancellationRequested && elapsed < ENABLE_TIMEOUT_MS)
            {
                int statusWord = GetStatusWord(slaveIndex);
                
                // 详细打印状态字的每个位
                Console.WriteLine($"从站{slaveIndex}当前状态字: 0x{statusWord:X4}");
                Console.WriteLine($"Ready to switch on: {(statusWord & 0x0001) != 0}");
                Console.WriteLine($"Switched on: {(statusWord & 0x0002) != 0}");
                Console.WriteLine($"Operation enabled: {(statusWord & 0x0004) != 0}");
                Console.WriteLine($"Fault: {(statusWord & 0x0008) != 0}");
                Console.WriteLine($"Voltage enabled: {(statusWord & 0x0010) != 0}");
                Console.WriteLine($"Quick stop: {(statusWord & 0x0020) != 0}");
                Console.WriteLine($"Switch on disabled: {(statusWord & 0x0040) != 0}");
                Console.WriteLine($"Warning: {(statusWord & 0x0080) != 0}");

                // 修改判断逻辑：检查状态字是否为0x1637或0x0637或0x0237
                bool isOperationEnabled = (statusWord & 0x0FFF) == 0x0637 || 
                                        (statusWord & 0x0FFF) == 0x1637 || 
                                        (statusWord & 0x0FFF) == 0x0237;
                
                if (isOperationEnabled)
                {
                    Console.WriteLine($"从站{slaveIndex}伺服已就绪");
                    return true;
                }
                
                // 如果检测到故障或警告，尝试清除
                if ((statusWord & 0x0088) != 0)
                {
                    Console.WriteLine($"从站{slaveIndex}出现故障或警告，尝试清除故障");
                    SetControlWord(slaveIndex, 0x0080);
                }
                
                Thread.Sleep(100);
                elapsed += 100;
            }
            
            if (elapsed >= ENABLE_TIMEOUT_MS)
            {
                Console.WriteLine($"等待从站{slaveIndex}伺服就绪超时，最后状态字: 0x{GetStatusWord(slaveIndex):X4}");
            }
            
            return false;
        }

        public bool EnableServo(int slaveIndex)
        {
            if (!GetSlaveOnlineStatus(slaveIndex) || !GetSlaveOperationalStatus(slaveIndex))
            {
                Console.WriteLine($"从站 {slaveIndex} 不在线或未处于运行状态，无法使能");
                return false;
            }
            
            Console.WriteLine($"开始使能从站 {slaveIndex}");
            
            // 设置控制字为0x0080 (关闭使能)
            SetControlWord(slaveIndex, 0x0080);
            Thread.Sleep(10);
            
            // 设置运行模式为速度模式(0x09)
            SetOperationMode(slaveIndex, 0x09);
            Thread.Sleep(10);
            
            // 状态检查和转换
            int elapsed = 0;
            bool enabled = false;
            
            while (!enabled && elapsed < ENABLE_TIMEOUT_MS)
            {
                UpdateSharedMemory();
                int statusWord = GetStatusWord(slaveIndex);
                
                // 详细打印状态字的每个位
                Console.WriteLine($"从站{slaveIndex}当前状态字: 0x{statusWord:X4}");
                Console.WriteLine($"Ready to switch on: {(statusWord & 0x0001) != 0}");
                Console.WriteLine($"Switched on: {(statusWord & 0x0002) != 0}");
                Console.WriteLine($"Operation enabled: {(statusWord & 0x0004) != 0}");
                Console.WriteLine($"Fault: {(statusWord & 0x0008) != 0}");
                Console.WriteLine($"Voltage enabled: {(statusWord & 0x0010) != 0}");
                Console.WriteLine($"Quick stop: {(statusWord & 0x0020) != 0}");
                Console.WriteLine($"Switch on disabled: {(statusWord & 0x0040) != 0}");
                Console.WriteLine($"Warning: {(statusWord & 0x0080) != 0}");
                
                // 状态机转换
                if ((statusWord & 0x004F) == 0x0040)
                {
                    // 准备好使能
                    Console.WriteLine($"从站{slaveIndex}准备好使能，发送控制字0x0006");
                    SetControlWord(slaveIndex, 0x0006);
                }
                else if ((statusWord & 0x006F) == 0x0021)
                {
                    // 已准备好切换到操作使能
                    Console.WriteLine($"从站{slaveIndex}准备好切换到操作使能，发送控制字0x0007");
                    SetControlWord(slaveIndex, 0x0007);
                }
                else if ((statusWord & 0x006F) == 0x0023)
                {
                    // 切换到操作使能状态
                    Console.WriteLine($"从站{slaveIndex}切换到操作使能状态，发送控制字0x000F");
                    SetControlWord(slaveIndex, 0x000F);
                }
                else if ((statusWord & 0x006F) == 0x0027)
                {
                    // 操作已使能
                    enabled = true;
                    Console.WriteLine($"从站 {slaveIndex} 已成功使能");
                    break;
                }
                else if ((statusWord & 0x0088) != 0)
                {
                    // 检测到故障或警告
                    Console.WriteLine($"从站{slaveIndex}出现故障或警告，尝试清除故障");
                    SetControlWord(slaveIndex, 0x0080); // 清除故障
                    Thread.Sleep(100);
                    SetControlWord(slaveIndex, 0x0006); // 重新开始使能流程
                }
                
                Thread.Sleep(100);
                elapsed += 100;
            }
            
            if (!enabled)
            {
                Console.WriteLine($"从站 {slaveIndex} 使能超时，最终状态字: 0x{GetStatusWord(slaveIndex):X4}");
                return false;
            }
            
            return true;
        }

        public void DisableServo()
        {
            _logger.Log("正在禁用所有伺服...");
            UpdateSharedMemory();
            
            for (int i = 0; i < 1; i++)
            {
                var controlWordField = typeof(EtherCATSharedMemory).GetField($"shm_slave{i}_rx_0x6040_control_word");
                if (controlWordField != null)
                {
                    controlWordField.SetValue(_sharedMemory, 0x0080);
                }
            }
            WriteSharedMemory();
            
            for (int i = 0; i < 1; i++)
            {
                var controlWordField = typeof(EtherCATSharedMemory).GetField($"shm_slave{i}_rx_0x6040_control_word");
                if (controlWordField != null)
                {
                    controlWordField.SetValue(_sharedMemory, 0x02);
                }
            }
            WriteSharedMemory();
            
            for (int i = 0; i < 1; i++)
            {
                var controlWordField = typeof(EtherCATSharedMemory).GetField($"shm_slave{i}_rx_0x6040_control_word");
                if (controlWordField != null)
                {
                    controlWordField.SetValue(_sharedMemory, 0x00);
                }
            }
            WriteSharedMemory();
            Console.WriteLine("所有伺服已禁用。");
        }

        public void SetVelocity(int velocity)
        {
            _logger.Log($"设置所有伺服速度: {velocity}");
            UpdateSharedMemory();
            
            for (int i = 0; i < 1; i++)
            {
                var targetSpeedField = typeof(EtherCATSharedMemory).GetField($"shm_slave{i}_rx_0x60ff_target_speed");
                if (targetSpeedField != null)
                {
                    targetSpeedField.SetValue(_sharedMemory, velocity);
                }
            }
            
            WriteSharedMemory();
        }

        public void UpdateStatus()
        {
            UpdateSharedMemory();
        }

        public void EnterRealtimeMode()
        {
            _logger.Log("正在进入实时模式...");
            try 
            {
                // 设置.NET线程优先级为最高
                Thread.CurrentThread.Priority = ThreadPriority.Highest;

                // 在Linux系统上设置实时调度策略
                if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
                {
                    const int SCHED_FIFO = 1;
                    
                    // 获取当前进程ID
                    int pid = getpid();
                    Console.WriteLine($"当前进程ID: {pid}");

                    // 获取SCHED_FIFO的最高优先级
                    int maxPriority = sched_get_priority_max(SCHED_FIFO);
                    Console.WriteLine($"SCHED_FIFO最高优先级: {maxPriority}");
                    
                    var schedParam = new sched_param { sched_priority = maxPriority };
                    
                    // 设置当前进程为SCHED_FIFO策略
                    int result = sched_setscheduler(pid, SCHED_FIFO, ref schedParam);
                    if (result != 0)
                    {
                        int error = Marshal.GetLastWin32Error();
                        Console.WriteLine($"警告: 设置SCHED_FIFO调度策略失败。错误码: {error}");
                        Console.WriteLine("请尝试使用以下命令运行程序：");
                        Console.WriteLine($"sudo chrt -f {maxPriority} dotnet run");
                        Console.WriteLine("或者使用以下命令：");
                        Console.WriteLine($"sudo nice -n -{maxPriority} dotnet run");
                    }
                    else 
                    {
                        Console.WriteLine($"成功设置实时调度策略SCHED_FIFO，优先级: {maxPriority}");
                    }
                }
                // 在Windows系统上设置进程优先级
                else if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
                {
                    using var process = Process.GetCurrentProcess();
                    process.PriorityClass = ProcessPriorityClass.RealTime;
                    Console.WriteLine("成功设置Windows进程优先级为实时");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"设置实时模式时出错: {ex.Message}");
            }
        }
        public async Task RunControlSequence(CancellationToken cancellationToken)
        {
            _logger.Log("开始运行控制序列...");
            try
            {
                Thread.CurrentThread.Priority = ThreadPriority.Highest;
                Console.WriteLine($"当前线程优先级已设置为: {Thread.CurrentThread.Priority}");

                bool servoEnabled = false;
                bool wasOnline = false;

                // 创建一个高优先级的任务来处理共享内存更新
                var memoryUpdateTask = Task.Run(async () =>
                {
                    Thread.CurrentThread.Priority = ThreadPriority.Highest;
                    Console.WriteLine($"共享内存更新线程优先级已设置为: {Thread.CurrentThread.Priority}");
                    
                    const int UPDATE_INTERVAL_MS = 1; // 1毫秒更新频率
                    int updateCount = 0;
                    var stopwatch = new Stopwatch();
                    const int targetSpeed = -100000000;
                    
                    while (!cancellationToken.IsCancellationRequested)
                    {
                        if (servoEnabled)
                        {
                            SetVelocity(targetSpeed);
                            // 读取共享内存
                            UpdateSharedMemory();
                            // 写回共享内存
                            WriteSharedMemory();
                            // Console.WriteLine($"");
                            
                           await Task.Delay(1, cancellationToken);
                        }
                        else
                        {
                            await Task.Delay(100, cancellationToken); // 未使能时降低检查频率
                        }
                    }
                }, cancellationToken);

                while (!cancellationToken.IsCancellationRequested)
                {
                    UpdateSharedMemory();
                    bool isOnline = _sharedMemory.shm_slave0_online_status == 1;
                    bool isOperational = _sharedMemory.shm_slave0_operational_status == 1;

                    // 检测从站状态变化
                    if (isOnline != wasOnline)
                    {
                        if (isOnline)
                        {
                            Console.WriteLine("从站已上线，正在检测运行状态...");
                            
                            while (!isOperational && !cancellationToken.IsCancellationRequested)
                            {
                                Console.WriteLine("等待从站进入OP状态...");
                                await Task.Delay(100, cancellationToken);
                                UpdateSharedMemory();
                                isOperational = _sharedMemory.shm_slave0_operational_status == 1;
                            }

                            if (isOperational)
                            {
                                Console.WriteLine("从站已进入运行状态，正在使能伺服...");
                                EnableServo(0);
                                if (WaitForServoReady(0, cancellationToken))
                                {
                                    servoEnabled = true;
                                    Console.WriteLine("伺服使能成功");
                                }
                                else
                                {
                                    Console.WriteLine("伺服使能失败");
                                    servoEnabled = false;
                                }
                            }
                        }
                        else
                        {
                            Console.WriteLine("从站离线，正在禁用伺服...");
                            DisableServo();
                            servoEnabled = false;
                        }
                        wasOnline = isOnline;
                    }

                    await Task.Delay(100, cancellationToken);
                }

                await memoryUpdateTask;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"控制序列出错: {ex.Message}");
            }
            finally
            {
                DisableServo();
                Console.WriteLine("进程已完成。");
            }
        }

        public void Dispose()
        {
            _logger.Log("正在释放EtherCAT控制器资源...");
            _viewAccessor?.Dispose();
            _memoryMappedFile?.Dispose();
        }
    }

    class Program
    {
        // 从站状态响应模型
        private static bool IsValidHexValue(string hex)
        {
            if (string.IsNullOrEmpty(hex)) return false;
            // 移除0x前缀后检查是否全为0
            string value = hex.StartsWith("0x") ? hex.Substring(2) : hex;
            return !value.All(c => c == '0');
        }

        // 常量定义
        private const int MAX_RETRIES = 10;
        private const int RETRY_DELAY_MS = 1000;
        private const int SERVO_SLAVE_COUNT = 1; // 驱动从站数量

        private static async Task<(int masterId, int slaveCount)?> GetMasterInfo(HttpClient client, string programName)
        {
            try
            {
                Console.WriteLine($"正在获取程序 {programName} 的主站信息...");
                var response = await client.GetAsync($"http://127.0.0.1:3000/api/programs/master-info?programName={programName}");

                if (!response.IsSuccessStatusCode)
                {
                    Console.WriteLine($"获取主站信息失败: {response.StatusCode}");
                    return null;
                }

                var responseContent = await response.Content.ReadAsStringAsync();
                var masterInfo = JsonSerializer.Deserialize<MasterInfoResponse>(responseContent);

                if (masterInfo.code != 0)
                {
                    Console.WriteLine($"获取主站信息错误: {masterInfo.msg}");
                    return null;
                }

                return (masterInfo.data.masterId, masterInfo.data.slaveCount);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取主站信息时发生错误: {ex.Message}");
                return null;
            }
        }

        private static async Task<bool> CheckSlavesStatus(HttpClient client, int targetMaster)
        {
            for (int i = 0; i < MAX_RETRIES; i++)
            {
                try
                {
                    Console.WriteLine($"正在检查主站{targetMaster}下从站状态，第{i + 1}次尝试...");
                    
                    var response = await client.GetAsync(
                        "http://127.0.0.1:3000/api/ethercat/all-slaves-status?detail=1"
                    );

                    if (!response.IsSuccessStatusCode)
                    {
                        Console.WriteLine($"获取从站状态失败: {response.StatusCode}");
                        continue;
                    }

                    var responseContent = await response.Content.ReadAsStringAsync();
                    var statusResponse = JsonSerializer.Deserialize<SlaveStatusDetailResponse>(responseContent);

                    if (statusResponse.code != 0)
                    {
                        Console.WriteLine($"API错误: {statusResponse.msg}");
                        continue;
                    }

                    var allSlaves = statusResponse.data.slaves;
                    
                    // 过滤出目标主站下的从站
                    var masterSlaves = allSlaves.Where(s => s.master == targetMaster).Take(SERVO_SLAVE_COUNT).ToList();
                    
                    if (masterSlaves.Count == 0)
                    {
                        Console.WriteLine($"未找到主站{targetMaster}的从站，等待后重试");
                        await Task.Delay(RETRY_DELAY_MS);
                        continue;
                    }
                    
                    // 检查所有从站是否都处于PREOP状态
                    var allPreop = masterSlaves.All(s => s.state == "PREOP");
                    var preopCount = masterSlaves.Count(s => s.state == "PREOP");
                    
                    if (!allPreop)
                    {
                        Console.WriteLine($"等待主站{targetMaster}的从站进入PREOP状态 (当前PREOP: {preopCount}, 总数: {masterSlaves.Count})");
                        Console.WriteLine("从站状态详情:");
                        foreach (var slave in masterSlaves)
                        {
                            Console.WriteLine($"- 从站 {slave.position}: 状态={slave.state}, AL状态={slave.alState}");
                        }
                        await Task.Delay(RETRY_DELAY_MS);
                        continue;
                    }
                    
                    // 检查每个从站的vendorId和productCode是否有效
                    bool allSlavesValid = true;
                    foreach (var slave in masterSlaves)
                    {
                        if (!IsValidHexValue(slave.vendorId) || !IsValidHexValue(slave.productCode))
                        {
                            Console.WriteLine($"从站 {slave.index} 的vendorId({slave.vendorId})或productCode({slave.productCode})无效");
                            allSlavesValid = false;
                            break;
                        }
                    }

                    if (!allSlavesValid)
                    {
                        await Task.Delay(RETRY_DELAY_MS);
                        continue;
                    }

                    Console.WriteLine($"主站{targetMaster}的所有从站状态检查通过 (总数: {masterSlaves.Count})");
                    return true;
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"检查从站状态时发生错误: {ex.Message}");
                    await Task.Delay(RETRY_DELAY_MS);
                }
            }

            Console.WriteLine($"从站状态检查失败，已达到最大重试次数({MAX_RETRIES})");
            return false;
        }

        private static async Task<string> StartMiddleware(string programName)
        {
            try
            {
                using var client = new HttpClient();
                client.DefaultRequestHeaders.Accept.Add(
                    new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json")
                );

                var requestData = new { programName };
                var jsonContent = JsonSerializer.Serialize(requestData);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                Console.WriteLine($"正在请求EtherCAT中间层服务 ({programName})...");
                var response = await client.PostAsync(
                    "http://127.0.0.1:3000/api/programs/start-middleware", 
                    content
                );

                if (!response.IsSuccessStatusCode)
                {
                    Console.WriteLine($"请求中间件失败: {response.StatusCode}");
                    return null;
                }

                var responseContent = await response.Content.ReadAsStringAsync();
                Console.WriteLine($"中间件返回数据: {responseContent}");

                // 解析JSON响应
                var responseData = JsonSerializer.Deserialize<MiddlewareResponse>(responseContent);
                if (responseData.code != 0)
                {
                    string errorMsg = !string.IsNullOrEmpty(responseData.msg) ? responseData.msg : "未知错误";
                    Console.WriteLine($"错误：中间件返回错误码 {responseData.code}，错误信息：{errorMsg}");
                    return null;
                }

                if (responseData.data == null || string.IsNullOrEmpty(responseData.data.shmFile))
                {
                    Console.WriteLine("错误：从中间件获取的共享内存路径为空");
                    return null;
                }
                
                var sharedMemoryFilePath = responseData.data.shmFile;
                Console.WriteLine($"启动成功，共享内存文件路径: {sharedMemoryFilePath}");
                return sharedMemoryFilePath;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"启动中间层失败: {ex.Message}");
                return null;
            }
        }
        static async Task Main(string[] args)
        {
            AsyncLogger mainLogger = null;
            try 
            {
                // 获取程序名并初始化日志服务
                string programName = Path.GetFileName(Process.GetCurrentProcess().MainModule?.FileName ?? "unknown");
                mainLogger = new AsyncLogger(programName);
                mainLogger.Start();
                mainLogger.Log("程序启动");

                // 创建HTTP客户端
                using var client = new HttpClient();
                client.DefaultRequestHeaders.Accept.Add(
                    new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json")
                );

                // 获取主站信息
                var masterInfo = await GetMasterInfo(client, programName);
                if (!masterInfo.HasValue)
                {
                    mainLogger.Log("无法获取主站信息，程序退出");
                    return;
                }
                int targetMaster = masterInfo.Value.masterId;
                mainLogger.Log($"获取到主站ID: {targetMaster}, 从站数量: {masterInfo.Value.slaveCount}");
                
                // 首先检查从站状态
                mainLogger.Log("开始检查从站状态...");
                if (!await CheckSlavesStatus(client, targetMaster))
                {
                    mainLogger.Log("从站状态检查失败，程序退出");
                    return;
                }
                
                // 启动两个中间层
                mainLogger.Log("正在启动中间层服务...");
                string sharedMemoryFilePath = await StartMiddleware(programName);
                
                if (string.IsNullOrEmpty(sharedMemoryFilePath))
                {
                    mainLogger.Log("无法获取第一个中间层共享内存路径，程序退出");
                    return;
                }
                
                // string ioModuleProgramName = "HXIO_Dummy";
                // string ioModuleSharedMemoryFilePath = await StartMiddleware(ioModuleProgramName);
                
                // if (string.IsNullOrEmpty(ioModuleSharedMemoryFilePath))
                // {
                //     mainLogger.Log("无法获取IO模块中间层共享内存路径，程序退出");
                //     return;
                // }
                
                // mainLogger.Log($"正在启动EtherCAT控制程序，伺服共享内存: {sharedMemoryFilePath}, IO模块共享内存: {ioModuleSharedMemoryFilePath}");

                using var controller = new EtherCATController(sharedMemoryFilePath, mainLogger);
                controller.EnterRealtimeMode();

                var cts = new CancellationTokenSource();

                // 添加SIGTERM信号处理
                AppDomain.CurrentDomain.ProcessExit += (s, e) => {
                    mainLogger.Log("收到SIGTERM信号，正在优雅退出...");
                    cts.Cancel();
                };

                Console.CancelKeyPress += (s, e) => {
                    e.Cancel = true;
                    mainLogger.Log("收到取消按键，正在退出...");
                    cts.Cancel();
                };

                try
                {
                    await controller.RunControlSequence(cts.Token);
                }
                catch (OperationCanceledException)
                {
                    mainLogger.Log("程序正在停止...");
                }
                catch (Exception ex)
                {
                    mainLogger.Log($"执行错误: {ex.Message}");
                }
                finally
                {
                    controller.DisableServo();
                    mainLogger.Log("即将停止中间层");
                    
                    try 
                    {
                        var jsonContent1 = JsonSerializer.Serialize(new { programName });
                        var stopContent1 = new StringContent(jsonContent1, Encoding.UTF8, "application/json");
                        var _ = client.PostAsync("http://127.0.0.1:3000/api/programs/stop-middleware", stopContent1).GetAwaiter().GetResult();
                        
                        // var jsonContent2 = JsonSerializer.Serialize(new { programName = ioModuleProgramName });
                        // var stopContent2 = new StringContent(jsonContent2, Encoding.UTF8, "application/json");
                        // var __ = client.PostAsync("http://127.0.0.1:3000/api/programs/stop-middleware", stopContent2).GetAwaiter().GetResult();
                        
                        mainLogger.Log("已停止所有中间层服务");
                    }
                    catch (Exception ex)
                    {
                        mainLogger.Log($"停止通知发送失败: {ex.Message}");
                    }
                    mainLogger.Log("程序已完全停止。");
                }
            }
            catch (Exception ex)
            {
                mainLogger?.Log($"程序启动错误: {ex.Message}");
            }
            finally
            {
                if (mainLogger != null)
                {
                    mainLogger.Stop();
                }
            }
        }
    }

    // 更新响应模型类
    public class SlaveStatusDetailResponse
    {
        public int code { get; set; }
        public string msg { get; set; }
        public SlaveStatusDetailData data { get; set; }
    }

    public class SlaveStatusDetailData
    {
        public int totalCount { get; set; }
        public int opCount { get; set; }
        public int preopCount { get; set; }
        public int initCount { get; set; }
        public int otherCount { get; set; }
        public List<SlaveDetail> slaves { get; set; }
    }

    public class SlaveDetail
    {
        public int index { get; set; }
        public int master { get; set; }
        public string name { get; set; }
        public string state { get; set; }
        public bool online { get; set; }
        public int operationalStatus { get; set; }
        public int alState { get; set; }
        public string vendorId { get; set; }
        public string productCode { get; set; }
        public string position { get; set; }
    }

    public class MiddlewareResponse
    {
        public int code { get; set; }
        public string msg { get; set; }
        public MiddlewareData data { get; set; }
    }

    public class MiddlewareData
    {
        public string shmFile { get; set; }
    }

    // 主站信息响应模型类
    public class MasterInfoResponse
    {
        public int code { get; set; }
        public string msg { get; set; }
        public MasterInfoData data { get; set; }
    }

    public class MasterInfoData
    {
        public int masterId { get; set; }
        public int slaveCount { get; set; }
    }
}