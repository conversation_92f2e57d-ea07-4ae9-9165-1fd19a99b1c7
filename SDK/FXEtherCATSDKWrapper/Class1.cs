/*
 * FXEtherCAT SDK C# 封装库 (动态加载版 - v3.1 更强路径探测)
 *
 * 功能说明：
 * - 自动探测应用程序根目录下的库文件，支持 lib{name}.so 和 {name}.so 两种命名方式。
 * - 支持在运行时指定要加载的底层C库文件。
 * - 默认加载 "sdk" 库，也可通过构造函数指定其他库名。
 * - 每个SDK实例管理自己的库生命周期，无资源泄露。
 * - 提供类型安全的C#接口，简化FXEtherCAT应用开发。
 *
 * 使用方式：
 * // 无论 sdk.dll/libsdk.so/sdk.so 在系统路径还是应用程序根目录，都能成功。
 * using (var sdk = new FXEtherCATSDK())
 * {
 *     sdk.WriteSlave(0, 0x6040, 0, controlWord);
 * }
 */
using System;
using System.IO;
using System.Reflection;
using System.Runtime.InteropServices;

namespace FXEtherCAT.SDK
{
    /// <summary>
    /// 表示EtherCAT从站设备的状态。
    /// </summary>
    public struct SlaveDeviceStatus
    {
        public bool IsOnline { get; internal set; }
        public bool IsOperational { get; internal set; }
        public int AlState { get; internal set; }
        public override string ToString()
        {
            return $"Online: {IsOnline}, Operational: {IsOperational}, AL State: 0x{AlState:X2}";
        }
    }

    /// <summary>
    /// FXEtherCAT SDK的C#封装类，提供对底层C库的访问。
    /// 此类实现了IDisposable接口，以确保非托管资源（如动态库句柄）被正确释放。
    /// </summary>
    public class FXEtherCATSDK : IDisposable
    {
        private bool _isDisposed = false;
        private bool _isInitialized = false;
        private readonly IntPtr _libHandle;
        private readonly string _loadedLibraryPath; // 记录最终加载的库路径

        #region C Function Delegates
        [UnmanagedFunctionPointer(CallingConvention.Cdecl)] private delegate int SdkConnectDelegate();
        [UnmanagedFunctionPointer(CallingConvention.Cdecl)] private delegate int SdkWriteSlaveDelegate(ushort slave_number, ushort index, byte subindex, int value);
        [UnmanagedFunctionPointer(CallingConvention.Cdecl)] private delegate int SdkReadSlaveDelegate(ushort slave_number, ushort index, byte subindex, out int value_ptr);
        [UnmanagedFunctionPointer(CallingConvention.Cdecl)] private delegate int SdkGetSlaveStatusDelegate(ushort slave_number, out int online_status, out int op_status, out int al_state);

        private readonly SdkConnectDelegate _sdk_connect;
        private readonly SdkWriteSlaveDelegate _sdk_write_slave;
        private readonly SdkReadSlaveDelegate _sdk_read_slave;
        private readonly SdkGetSlaveStatusDelegate _sdk_get_slave_status;
        #endregion

        /// <summary>
        /// 使用默认SDK库 ("sdk") 初始化。
        /// </summary>
        public FXEtherCATSDK() : this("sdk")
        {
        }

        /// <summary>
        /// 使用指定名称的SDK库初始化。
        /// </summary>
        public FXEtherCATSDK(string programName)
        {
            if (string.IsNullOrWhiteSpace(programName))
            {
                throw new ArgumentException("库名称不能为空", nameof(programName));
            }

            try
            {
                // 智能路径探测逻辑
                _libHandle = LoadLibraryWithProbe(programName, out _loadedLibraryPath);

                T GetDelegate<T>(string funcName) where T : Delegate
                {
                    IntPtr funcPtr = NativeLibrary.GetExport(_libHandle, funcName);
                    return Marshal.GetDelegateForFunctionPointer<T>(funcPtr);
                }

                _sdk_connect = GetDelegate<SdkConnectDelegate>("sdk_connect");
                _sdk_write_slave = GetDelegate<SdkWriteSlaveDelegate>("sdk_write_slave");
                _sdk_read_slave = GetDelegate<SdkReadSlaveDelegate>("sdk_read_slave");
                _sdk_get_slave_status = GetDelegate<SdkGetSlaveStatusDelegate>("sdk_get_slave_status");
            }
            catch (Exception ex)
            {
                if (_libHandle != IntPtr.Zero) NativeLibrary.Free(_libHandle);
                throw new SDKInitializationException($"加载SDK库 '{programName}' 或获取函数地址失败: {ex.Message}", ex);
            }

            int initResult = _sdk_connect();
            if (initResult != 0)
            {
                Dispose();
                throw new SDKInitializationException($"SDK初始化失败，库: '{_loadedLibraryPath}'，错误代码: {initResult}，请检查底层日志");
            }
            _isInitialized = true;
            Console.WriteLine($"C# Wrapper: SDK初始化成功，使用库: '{_loadedLibraryPath}'");
        }

        private static IntPtr LoadLibraryWithProbe(string libraryName, out string loadedPath)
        {
            Console.WriteLine($"C# Wrapper: 尝试加载库 '{libraryName}'");
            Console.WriteLine($"C# Wrapper: AppContext.BaseDirectory = '{AppContext.BaseDirectory}'");
            Console.WriteLine($"C# Wrapper: Current Directory = '{Directory.GetCurrentDirectory()}'");

            // 步骤1: 尝试直接加载，这会搜索系统标准路径和应用程序目录
            if (NativeLibrary.TryLoad(libraryName, typeof(FXEtherCATSDK).Assembly, DllImportSearchPath.ApplicationDirectory | DllImportSearchPath.System32, out IntPtr handle))
            {
                loadedPath = libraryName;
                Console.WriteLine($"C# Wrapper: 步骤1成功加载库 '{libraryName}'");
                return handle;
            }
            Console.WriteLine($"C# Wrapper: 步骤1失败，开始详细搜索...");

            // 步骤2: 在多个可能的目录中搜索库文件
            string[] searchDirectories = {
                AppContext.BaseDirectory,                    // 应用程序基目录
                Directory.GetCurrentDirectory(),             // 当前工作目录
                Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location) ?? "", // 程序集所在目录
                "/usr/lib", "/usr/local/lib", "/lib"        // 系统库目录
            };

            foreach (string searchDir in searchDirectories)
            {
                if (string.IsNullOrEmpty(searchDir) || !Directory.Exists(searchDir)) continue;

                string? localPathToLoad = null;

                if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
                {
                    localPathToLoad = Path.Combine(searchDir, $"{libraryName}.dll");
                }
                else if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
                {
                    // 在Linux上，检查两种常见的命名约定
                    string libName1 = $"lib{libraryName}.so";
                    string libName2 = $"{libraryName}.so";

                    string localPath1 = Path.Combine(searchDir, libName1);
                    string localPath2 = Path.Combine(searchDir, libName2);

                    if (File.Exists(localPath1)) localPathToLoad = localPath1;
                    else if (File.Exists(localPath2)) localPathToLoad = localPath2;
                }
                else if (RuntimeInformation.IsOSPlatform(OSPlatform.OSX))
                {
                    localPathToLoad = Path.Combine(searchDir, $"lib{libraryName}.dylib");
                }

                if (localPathToLoad != null && File.Exists(localPathToLoad))
                {
                    if (NativeLibrary.TryLoad(localPathToLoad, out handle))
                    {
                        loadedPath = localPathToLoad;
                        Console.WriteLine($"C# Wrapper: 成功加载库文件: {localPathToLoad}");
                        return handle;
                    }
                }
            }

            // 如果所有尝试都失败，则抛出详细的错误信息
            string searchedPaths = string.Join(", ", searchDirectories.Where(d => !string.IsNullOrEmpty(d)));
            throw new DllNotFoundException($"在以下目录中都找不到库文件 '{libraryName}': {searchedPaths}。已尝试的名称可能包括 '{libraryName}.dll', 'lib{libraryName}.so', '{libraryName}.so' 等。");
        }
        
        public bool IsInitialized() => _isInitialized;
        public bool WriteSlave(ushort slaveNumber, ushort index, byte subindex, int value) { if (_isDisposed) throw new ObjectDisposedException(nameof(FXEtherCATSDK)); if (!_isInitialized) throw new InvalidOperationException("SDK未初始化"); int r = _sdk_write_slave(slaveNumber, index, subindex, value); if (r != 0) Console.WriteLine($"C# Wrapper: 从站{slaveNumber}写入失败, err={r}"); return r == 0; }
        public bool ReadSlave(ushort slaveNumber, ushort index, byte subindex, out int value) { if (_isDisposed) throw new ObjectDisposedException(nameof(FXEtherCATSDK)); if (!_isInitialized) throw new InvalidOperationException("SDK未初始化"); int r = _sdk_read_slave(slaveNumber, index, subindex, out value); if (r != 0) Console.WriteLine($"C# Wrapper: 从站{slaveNumber}读取失败, err={r}"); return r == 0; }
        public bool GetSlaveDeviceStatus(ushort slaveNumber, out SlaveDeviceStatus status) { if (_isDisposed) throw new ObjectDisposedException(nameof(FXEtherCATSDK)); if (!_isInitialized) throw new InvalidOperationException("SDK未初始化"); status = new SlaveDeviceStatus(); int r = _sdk_get_slave_status(slaveNumber, out int o, out int op, out int a); if (r == 0) { status.IsOnline = o != 0; status.IsOperational = op != 0; status.AlState = a; return true; } else { Console.WriteLine($"C# Wrapper: 获取从站{slaveNumber}状态失败, err={r}"); return false; } }
        
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (_isDisposed) return;
            if (disposing)
            {
                // 释放托管资源
            }
            if (_libHandle != IntPtr.Zero)
            {
                if (_isInitialized)
                {
                    Console.WriteLine("C# Wrapper: SDK准备断开连接");
                }
                NativeLibrary.Free(_libHandle);
                Console.WriteLine("C# Wrapper: 释放SDK库句柄");
            }
            _isDisposed = true;
            _isInitialized = false;
        }

        ~FXEtherCATSDK()
        {
            Dispose(false);
        }
    }

    /// <summary>
    /// 表示在SDK初始化期间发生的错误。
    /// </summary>
    public class SDKInitializationException : Exception
    {
        public SDKInitializationException(string message) : base(message) { }
        public SDKInitializationException(string message, Exception innerException) : base(message, innerException) { }
    }

    /// <summary>
    /// 表示在SDK操作（如读写）期间发生的错误。
    /// </summary>
    public class SDKOperationException : Exception
    {
        public SDKOperationException(string message) : base(message) { }
    }
}