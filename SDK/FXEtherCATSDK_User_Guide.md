# FXEtherCATSDK C# 封装库保姆级使用指南

**版本**: 1.0.0.1
**最后更新日期**: 2025年6月24日
**文档目的**: 帮助开发者从直接操作共享内存的传统方式，平滑过渡到使用 `FXEtherCATSDK` C# 封装库，同时展示如何在真实场景中利用 SDK 简化复杂的控制逻辑。

---

## 1. 为什么要从共享内存过渡到 C# SDK？

如果您之前的代码是通过 `MemoryMappedFile` 直接读写一个庞大的共享内存结构体，那么本指南将是您的福音。切换到 C# SDK 会带来质的飞跃：

*   **告别"魔法数字"和晦涩的变量名**:
    *   **之前**: `_sharedMemory.shm_slave0_rx_0x6040_control_word = 15; WriteSharedMemory();`
    *   **现在**: `_sdk.WriteSlave(0, 0x6040, 0, 15);`
    *   代码更具可读性，意图一目了然。

*   **拥抱类型安全**:
    *   C# 编译器会检查您传入的参数类型，从编译阶段就避免了很多低级错误。

*   **自动管理非托管资源**:
    *   **之前**: 您需要定义复杂的 `struct`，手动管理 `MemoryMappedFile` 和 `MemoryMappedViewAccessor` 的生命周期，一旦忘记 `Dispose` 或程序异常退出，极易造成内存和句柄泄漏。
    *   **现在**: 只需将 `FXEtherCATSDK` 对象包裹在 `using` 语句中，SDK 会自动、安全地为您处理所有资源的分配与释放。

*   **简化的错误处理和状态获取**:
    *   **之前**: 您需要从一个巨大的结构体中找到对应的字段来获取状态，例如 `_sharedMemory.shm_slave0_online_status == 1`。
    *   **现在**: `_sdk.GetSlaveDeviceStatus(0, out SlaveDeviceStatus status)` 直接返回一个包含所有状态的结构化对象，代码更优雅。

## 2. 核心概念回顾

在开始之前，我们快速回顾一下 API 文档中的几个核心概念：

*   `FXEtherCATSDK` 类: 这是您交互的唯一入口，封装了所有底层操作。
*   `SlaveDeviceStatus` 结构体: 一个干净的容器，包含了从站的在线、运行和AL状态。
*   `SDKInitializationException` 异常: 当 SDK 无法连接到底层服务（例如 `middleware_host` 未运行）时，构造函数会抛出此异常。

## 3. 从共享内存到 SDK：一步一脚印

### 第 1 步：项目设置

#### 平台支持

FXEtherCATSDK 支持以下 Linux 平台：

* **Linux x64** (`linux-x64`) - 适用于64位Intel/AMD处理器的Linux系统 （如飞智机：64位Intel/AMD处理器）
* **Linux ARM64** (`linux-arm64`) - 适用于64位ARM处理器（如T507：ARM Cortex-A53等）
* **Linux ARM32** (`linux-arm`) - 适用于32位ARM处理器（如RK3506：ARM Cortex-A7等）

#### 添加DLL引用

根据您的目标平台，选择对应的DLL文件：

```
发布包结构：
├── linux-x64/
│   └── FXEtherCATSDKWrapper.dll    # 用于 Linux x64 系统
├── linux-arm64/
│   └── FXEtherCATSDKWrapper.dll    # 用于 Linux ARM64 系统 
├── linux-arm/
│   └── FXEtherCATSDKWrapper.dll    # 用于 Linux ARM32 系统
```

**操作步骤：**
1. 根据您的目标Linux平台，选择对应文件夹中的 `FXEtherCATSDKWrapper.dll`
2. 在您的 C# 项目中，添加对该DLL的引用
3. 确保在部署时使用正确平台的DLL版本

### 第 2 步：替换连接与断开逻辑

**之前 (基于 `L5N_original_control.cs`)**
```csharp
// 需要手动通过HTTP等方式启动中间件，并获取共享内存文件路径
string shmPath = await StartMiddleware("MyProgram");

// 手动创建和管理内存映射文件
private MemoryMappedFile _memoryMappedFile;
private MemoryMappedViewAccessor _viewAccessor;

_memoryMappedFile = MemoryMappedFile.CreateFromFile(shmPath, FileMode.Open);
_viewAccessor = _memoryMappedFile.CreateViewAccessor();

// ... 程序逻辑 ...

// 在程序退出时，必须手动调用 Dispose
public void Dispose()
{
    _viewAccessor?.Dispose();
    _memoryMappedFile?.Dispose();
}
```

**现在**
```csharp
using FXEtherCAT.SDK;
using System;

try
{
    // 确保底层中间件已启动
    await StartMiddleware("MyProgram", sdk: true);

    // 使用 'using' 语句，SDK的连接和资源释放被完全自动化
    using (var sdk = new FXEtherCATSDK()) 
    {
        Console.WriteLine("SDK 连接成功！");
        // ... 所有与从站的交互都在这里进行 ...
    } // sdk.Dispose() 会在这里被自动调用，安全无忧！
}
catch (SDKInitializationException ex)
{
    Console.WriteLine($"SDK 初始化失败: {ex.Message}");
}
```

### 第 3 步：状态读取的进化

**之前 (基于 `L5N_original_control.cs`)**
```csharp
// 需要一个庞大的结构体来映射内存
[StructLayout(LayoutKind.Sequential)]
public struct EtherCATSharedMemory {
    public int shm_slave0_online_status;
    public int shm_slave0_operational_status;
    public int shm_slave0_al_state;
    // ... 可能还有上百个其他字段 ...
}

// 在代码中读取
_viewAccessor.Read(0, out _sharedMemory);
bool isOnline = _sharedMemory.shm_slave0_online_status == 1;
bool isOperational = _sharedMemory.shm_slave0_operational_status == 1;
int alState = _sharedMemory.shm_slave0_al_state;
Console.WriteLine($"[从站 0] Online: {isOnline}, Op: {isOperational}, AL-State: {alState}");
```

**现在**
```csharp
if (sdk.GetSlaveDeviceStatus(0, out SlaveDeviceStatus status))
{
    Console.WriteLine($"[从站 0] Online: {status.IsOnline}, Op: {status.IsOperational}, AL-State: 0x{status.AlState:X2}");
}
else
{
    Console.WriteLine("获取从站 0 状态失败。");
}
```
**优势**: 您得到了一个结构化的 `status` 对象，代码更清晰，且避免了定义和维护庞大的共享内存结构体。

### 第 4 步：读写数据 (PDO) 的革命

**之前 (基于 `L5N_original_control.cs`)**
```csharp
// 写入控制字
_sharedMemory.shm_slave0_rx_0x6040_control_word = 0x0F;
_viewAccessor.Write(0, ref _sharedMemory); // 每次写入都需要写回整个结构体

// 读取状态字
_viewAccessor.Read(0, out _sharedMemory); // 每次读取前都可能需要重新加载整个结构体
int statusWord = _sharedMemory.shm_slave0_tx_0x6041_status_word;
```

**现在**
```csharp
// 写入控制字 0x0F 到从站 0 的 0x6040:0
if (sdk.WriteSlave(0, 0x6040, 0, 0x0F))
{
    Console.WriteLine("成功写入控制字: 0x0F");
}

// 从从站 0 的 0x6041:0 读取状态字
if (sdk.ReadSlave(0, 0x6041, 0, out int statusWord))
{
    Console.WriteLine($"读取到状态字: 0x{statusWord:X4}");
}
```
**重点**: `index` (0x6040) 和 `subindex` (0) 是关键！它们是 EtherCAT 从站对象字典 (OD) 中的地址，**必须严格按照您所使用的从站设备的官方手册来填写。**

## 4. 实战演练：重构伺服使能逻辑

理论是苍白的，让我们来看一个最能体现 SDK 价值的真实场景：**使能一个伺服驱动器**。这是一个典型的状态机操作，涉及多次读写和判断。

### 改造前 (基于 `L5N_original_control.cs`)

代码冗长，充斥着对共享内存的直接读写和位运算，业务逻辑和底层实现紧紧耦合。

```csharp
public bool EnableServo(int slaveIndex)
{
    // ...
    // 状态机转换
    int elapsed = 0;
    while (elapsed < ENABLE_TIMEOUT_MS)
    {
        UpdateSharedMemory(); // 每次循环都读取整个共享内存
        int statusWord = _sharedMemory.shm_slave0_tx_0x6041_status_word;
        
        // 状态机转换：直接操作位和魔法数字
        if ((statusWord & 0x004F) == 0x0040) // "Switch on disabled"
        {
            Console.WriteLine("状态: [Switch On Disabled], 发送 0x0006");
            _sharedMemory.shm_slave0_rx_0x6040_control_word = 0x0006;
            WriteSharedMemory(); // 写回整个内存块
        }
        else if ((statusWord & 0x006F) == 0x0021) // "Ready to switch on"
        {
            Console.WriteLine("状态: [Ready to Switch On], 发送 0x0007");
            _sharedMemory.shm_slave0_rx_0x6040_control_word = 0x0007;
            WriteSharedMemory();
        }
        else if ((statusWord & 0x006F) == 0x0023) // "Switched on"
        {
            Console.WriteLine("状态: [Switched On], 发送 0x000F");
            _sharedMemory.shm_slave0_rx_0x6040_control_word = 0x000F;
            WriteSharedMemory();
        }
        else if ((statusWord & 0x006F) == 0x0027) // "Operation enabled"
        {
            Console.WriteLine("伺服已成功使能");
            return true;
        }
        // ... 其他状态处理 ...
        Thread.Sleep(100);
        elapsed += 100;
    }
    return false;
}
```

### 改造后 (基于 `Demo/Controller/control.cs`)

业务逻辑被清晰地分离出来。代码的可读性和可维护性大大增强。

```csharp
// Controller 内可以封装一些辅助方法
private int GetStatusWord(ushort slaveIndex)
{
    _sdk.ReadSlave(slaveIndex, 0x6041, 0, out int statusWord);
    return statusWord;
}

private void SetControlWord(ushort slaveIndex, int value)
{
    _sdk.WriteSlave(slaveIndex, 0x6040, 0, value);
}

// 使能逻辑本身变得非常清晰
public bool EnableServo(ushort slaveIndex = 0)
{
    _logger.Log($"开始使能从站 {slaveIndex}");
    SetOperationMode(slaveIndex, 9); // 设置为速度模式
    Thread.Sleep(50);
    SetControlWord(slaveIndex, 0x0080); // 清除故障
    Thread.Sleep(50);

    int elapsed = 0;
    while (elapsed < ENABLE_TIMEOUT_MS)
    {
        int statusWord = GetStatusWord(slaveIndex);

        // 状态机逻辑现在只关注业务，不关心如何读写内存
        if ((statusWord & 0x004F) == 0x0040) // "Switch on disabled"
        {
            Console.WriteLine($"状态: [Switch On Disabled], 发送控制字 0x0006");
            SetControlWord(slaveIndex, 0x0006);
        }
        else if ((statusWord & 0x006F) == 0x0021) // "Ready to switch on"
        {
            Console.WriteLine($"状态: [Ready to Switch On], 发送控制字 0x0007");
            SetControlWord(slaveIndex, 0x0007);
        }
        else if ((statusWord & 0x006F) == 0x0023) // "Switched on"
        {
            Console.WriteLine($"状态: [Switched On], 发送控制字 0x000F");
            SetControlWord(slaveIndex, 0x000F);
        }
        else if ((statusWord & 0x006F) == 0x0027) // "Operation enabled"
        {
            Console.WriteLine($"从站 {slaveIndex} 已成功使能.");
            return true;
        }
        // ... 其他状态处理 ...
        Thread.Sleep(100);
        elapsed += 100;
    }
    _logger.Log($"从站 {slaveIndex} 使能超时");
    return false;
}
```

## 5. 完整控制器改造示例

下面是一个更完整的 `Controller` 类，展示了从依赖共享内存到依赖 SDK 的整体演变。

### 改造前: `EtherCATController`
```csharp
public class EtherCATController : IDisposable
{
    private readonly MemoryMappedFile _memoryMappedFile;
    private readonly MemoryMappedViewAccessor _viewAccessor;
    private EtherCATSharedMemory _sharedMemory; // 依赖庞大的结构体

    public EtherCATController(string shmPath, ...)
    {
        _memoryMappedFile = MemoryMappedFile.CreateFromFile(shmPath, FileMode.Open);
        _viewAccessor = _memoryMappedFile.CreateViewAccessor();
        _sharedMemory = new EtherCATSharedMemory();
    }
    
    private void SetControlWord(int slaveIndex, int value)
    {
        UpdateSharedMemory();
        _sharedMemory.shm_slave0_rx_0x6040_control_word = value;
        WriteSharedMemory();
    }

    // ... 其他直接操作 _sharedMemory 的方法 ...

    public void Dispose() { /* ...手动释放资源... */ }
}
```

### 改造后: `FXEtherCATController`
```csharp
public class FXEtherCATController : IDisposable
{
    private readonly FXEtherCATSDK _sdk; // 只依赖 SDK
    private readonly AsyncLogger _logger;

    public FXEtherCATController(AsyncLogger logger)
    {
        _logger = logger;
        _sdk = new FXEtherCATSDK(); // 在构造函数中初始化
    }

    private void SetControlWord(ushort slaveIndex, int value)
    {
        if (!_sdk.WriteSlave(slaveIndex, 0x6040, 0, value))
        {
            _logger.Log($"Warning: 写入 ControlWord (0x6040) 失败");
        }
    }

    // ... 其他基于 _sdk 的封装方法 ...

    public void Dispose()
    {
        _logger.Log("释放 FXEtherCATController 资源...");
        _sdk?.Dispose(); // Dispose SDK 即可
    }
}
```

---

我们相信，通过这份更新后的指南，您已经完全掌握了如何利用这个 C# 封装库来重构现有代码，使其更简洁、更健壮、更易于维护。现在就开始享受编码的乐趣吧！ 