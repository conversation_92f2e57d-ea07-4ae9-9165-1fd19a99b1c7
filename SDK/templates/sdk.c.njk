{#-
  Nunjucks template for the SDK C file.
  This template receives the pre-processed 'config' object.
  All logic and string generation is done in the TS service.
-#}
#define _GNU_SOURCE
#include "{% if id %}{{ id }}{% else %}sdk{% endif %}.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <pthread.h>
#include <unistd.h> // For sleep, usleep
#include <sys/mman.h>
#include <fcntl.h>
#include <sched.h>
#include <sys/stat.h>
#include <time.h>   // For clock_gettime, CLOCK_MONOTONIC, etc.
#include <errno.h>  // For errno

#include "ecrt.h" // EtherCAT master library

// --- Internal SDK Configuration and State ---

// Time definitions
#define NSEC_PER_SEC (1000000000L)
#define TIMESPEC2NS(T) ((uint64_t) (T).tv_sec * NSEC_PER_SEC + (T).tv_nsec)
#define TASK_FREQUENCY {{ config.taskFrequency | default(4000) }} // Hz
#define PERIOD_NS (NSEC_PER_SEC / TASK_FREQUENCY)
#define STATUS_UPDATE_INTERVAL_MS 1000
#define STATUS_UPDATE_PERIOD ((TASK_FREQUENCY * STATUS_UPDATE_INTERVAL_MS) / 1000)


// PDO/DC configuration thread parameters
typedef struct {
    ec_slave_config_t *slave_config;
    ec_sync_info_t *sync_info;
    int *result;
} config_thread_param_t;

typedef struct {
    ec_slave_config_t *slave_config;
    uint16_t assign_activate;
    uint32_t sync0_cycle;
    uint32_t sync0_shift;
    uint32_t sync1_cycle;
    uint32_t sync1_shift;
    int *result;
} dc_config_thread_param_t;

// Shared memory configuration
#define INTERNAL_SHM_FILE "{{ config.id | default('qb3tau3h4rlo2tc_L5N_Test_shm_sdk') }}_shm_sdk"

// Internal representation of shared memory data
typedef struct {
{% for slave in config.slaves %}
    int32_t shm_slave{{ slave.slave_index }}_online_status;
    int32_t shm_slave{{ slave.slave_index }}_operational_status;
    int32_t shm_slave{{ slave.slave_index }}_al_state;
{% for pdo in slave.active_rx_pdos %}
    {{ pdo.shm_var_definition }}
{% endfor %}
{% for pdo in slave.active_tx_pdos %}
    {{ pdo.shm_var_definition }}
{% endfor %}
{% endfor %}
} sdk_internal_shm_data_t;

static sdk_internal_shm_data_t *g_sdk_shm_ptr = NULL;
static size_t g_sdk_shm_size = sizeof(sdk_internal_shm_data_t);
static char g_shm_name[256] = INTERNAL_SHM_FILE;

// EtherCAT master and domain data
static ec_master_t *g_master = NULL;
static ec_master_state_t g_master_state = {};
static ec_domain_t *g_domain1 = NULL;
static ec_domain_state_t g_domain1_state = {};
static uint8_t *g_domain1_pd = NULL; // Process data

// Slave configuration data
{% for slave in config.slaves -%}
static ec_slave_config_t *g_sc_slave{{ slave.slave_index }} = NULL;
static ec_slave_config_state_t g_sc_slave{{ slave.slave_index }}_state = {};
{% endfor %}

// Cyclic task control
static volatile int g_run_cyclic_task = 0;
static pthread_t g_cyclic_task_thread_id;
static pthread_mutex_t g_shm_mutex = PTHREAD_MUTEX_INITIALIZER;
static int g_core_affinity = {{ config.coreAffinity | default(-1) }}; // -1 means no affinity set

// PDO entry offsets
static struct {
{% for slave in config.slaves %}
{% for pdo in slave.active_rx_pdos %}
{% for line in pdo.offset_var_definitions %}
    {{ line }}
{% endfor %}
{% endfor %}
{% for pdo in slave.active_tx_pdos %}
{% for line in pdo.offset_var_definitions %}
    {{ line }}
{% endfor %}
{% endfor %}
{% endfor %}
} g_pdo_offsets;

// PDO registration list
{% for slave in config.slaves -%}
{{ slave.pos_define }}
{{ slave.vid_pid_define }}
{% endfor %}
const static ec_pdo_entry_reg_t domain1_regs[] = {
{% for slave in config.slaves %}
{% for pdo in slave.active_rx_pdos %}
    {{ pdo.registration_line }}
{% endfor %}
{% for pdo in slave.active_tx_pdos %}
    {{ pdo.registration_line }}
{% endfor %}
{% endfor %}
    {}
};

{% for slave in config.slaves %}
// PDO entries for slave {{ slave.slave_index }}
static ec_pdo_entry_info_t slave{{ slave.slave_index }}_pdo_entries[] = {
{{ slave.generated_c.pdo_entries | indent(4, true) }}
};

// PDO info for slave {{ slave.slave_index }}
static ec_pdo_info_t slave{{ slave.slave_index }}_pdos[] = {
{{ slave.generated_c.pdo_info | indent(4, true) }}
};

// Sync manager info for slave {{ slave.slave_index }}
static ec_sync_info_t slave{{ slave.slave_index }}_syncs[] = {
{{ slave.generated_c.sync_info | indent(4, true) }}
};
{% endfor %}

// --- Helper Functions ---

static void* config_slave_thread_func(void *arg) {
    config_thread_param_t *param = (config_thread_param_t *)arg;
    *(param->result) = ecrt_slave_config_pdos(param->slave_config, EC_END, param->sync_info);
    return NULL;
}

static void* dc_config_thread_func(void *arg) {
    dc_config_thread_param_t *param = (dc_config_thread_param_t *)arg;
    int ret = ecrt_slave_config_dc(
        param->slave_config,
        param->assign_activate,
        param->sync0_cycle,
        param->sync0_shift,
        param->sync1_cycle,
        param->sync1_shift
    );
    *(param->result) = ret;
    return NULL;
}

static void internal_create_shm() {
    int fd = shm_open(g_shm_name, O_CREAT | O_RDWR, 0666);
    if (fd < 0) {
        perror("SDK: shm_open failed");
        g_sdk_shm_ptr = MAP_FAILED;
        return;
    }
    if (ftruncate(fd, g_sdk_shm_size) < 0) {
        perror("SDK: ftruncate failed");
        close(fd);
        shm_unlink(g_shm_name);
        g_sdk_shm_ptr = MAP_FAILED;
        return;
    }
    g_sdk_shm_ptr = (sdk_internal_shm_data_t *)mmap(NULL, g_sdk_shm_size,
                                                 PROT_READ | PROT_WRITE,
                                                 MAP_SHARED, fd, 0);
    if (g_sdk_shm_ptr == MAP_FAILED) {
        perror("SDK: mmap failed");
        close(fd);
        shm_unlink(g_shm_name);
        return;
    }
    close(fd);
    memset(g_sdk_shm_ptr, 0, g_sdk_shm_size);
    fprintf(stdout, "SDK: Shared memory '%s' created/opened successfully.\n", g_shm_name);
}

static void internal_cleanup_shm() {
    if (g_sdk_shm_ptr != NULL && g_sdk_shm_ptr != MAP_FAILED) {
        if (munmap(g_sdk_shm_ptr, g_sdk_shm_size) == -1) {
            perror("SDK: munmap failed");
        }
        g_sdk_shm_ptr = NULL; 
        if (shm_unlink(g_shm_name) == -1) {
            if (errno != ENOENT) {
                perror("SDK: shm_unlink failed");
            }
        }
        fprintf(stdout, "SDK: Shared memory '%s' unmapped and unlinked.\n", g_shm_name);
    } else {
        shm_unlink(g_shm_name);
    }
}

static void timespec_add_ns(struct timespec *ts, uint64_t ns) {
    ts->tv_nsec += ns;
    while (ts->tv_nsec >= NSEC_PER_SEC) {
        ts->tv_nsec -= NSEC_PER_SEC;
        ts->tv_sec++;
    }
}

// --- Cyclic Task ---
static void *cyclic_task_main(void *arg) {
    (void)arg; // Unused
    struct timespec wakeup_time;
    int cycle_counter = 0;

    // Set CPU affinity if requested
    if (g_core_affinity != -1) {
        cpu_set_t cpuset;
        CPU_ZERO(&cpuset);
        CPU_SET(g_core_affinity, &cpuset);
        if (pthread_setaffinity_np(pthread_self(), sizeof(cpu_set_t), &cpuset) != 0) {
            perror("SDK: pthread_setaffinity_np failed");
        } else {
            fprintf(stdout, "SDK: Cyclic task pinned to CPU core %d\n", g_core_affinity);
        }
    }
    
    // Set real-time priority for this thread
    struct sched_param sparam;
    sparam.sched_priority = sched_get_priority_max(SCHED_FIFO);
    if (sched_setscheduler(0, SCHED_FIFO, &sparam) == -1) {
        perror("SDK: sched_setscheduler for cyclic task failed (run with sudo?)");
    } else {
         fprintf(stdout, "SDK: Cyclic task using SCHED_FIFO priority %d.\n", sparam.sched_priority);
    }

    clock_gettime(CLOCK_MONOTONIC, &wakeup_time);

    while (g_run_cyclic_task) {
        timespec_add_ns(&wakeup_time, PERIOD_NS);
        clock_nanosleep(CLOCK_MONOTONIC, TIMER_ABSTIME, &wakeup_time, NULL);

        ecrt_master_application_time(g_master, TIMESPEC2NS(wakeup_time));
        ecrt_master_receive(g_master);
        
        ecrt_domain_process(g_domain1);

        pthread_mutex_lock(&g_shm_mutex);

        if (g_sdk_shm_ptr) { // Check if SHM is valid
            if (cycle_counter > 0) {
                cycle_counter--;
            } else {
                cycle_counter = STATUS_UPDATE_PERIOD;
                {% for slave in config.slaves %}
                ecrt_slave_config_state(g_sc_slave{{ slave.slave_index }}, &g_sc_slave{{ slave.slave_index }}_state);
                g_sdk_shm_ptr->shm_slave{{ slave.slave_index }}_online_status = g_sc_slave{{ slave.slave_index }}_state.online;
                g_sdk_shm_ptr->shm_slave{{ slave.slave_index }}_operational_status = g_sc_slave{{ slave.slave_index }}_state.operational;
                g_sdk_shm_ptr->shm_slave{{ slave.slave_index }}_al_state = g_sc_slave{{ slave.slave_index }}_state.al_state;
                {% endfor %}
            }

            if (g_domain1_pd) { // Check if PD is valid
                // Read TxPDO data from EtherCAT domain to SHM
                {% for slave in config.slaves %}
                {% for pdo in slave.active_tx_pdos %}
                {{ pdo.cyclic_line }}
                {% endfor %}
                {% endfor %}

                // Write RxPDO data from SHM to EtherCAT domain
                {% for slave in config.slaves %}
                {% for pdo in slave.active_rx_pdos %}
                {{ pdo.cyclic_line }}
                {% endfor %}
                {% endfor %}
            }
        }
        
        pthread_mutex_unlock(&g_shm_mutex);

        ecrt_domain_queue(g_domain1);
        ecrt_master_sync_slave_clocks(g_master);
        ecrt_master_sync_reference_clock(g_master);
        ecrt_master_send(g_master);
    }
    fprintf(stdout, "SDK: Cyclic task finished.\n");
    return NULL;
}


// --- Public SDK API Functions ---

__attribute__((visibility("default"))) int sdk_init() {
    fprintf(stdout, "SDK: Initializing...\n");
    
    if (mlockall(MCL_CURRENT | MCL_FUTURE) == -1) {
        perror("SDK: mlockall failed");
    }

    internal_create_shm();
    if (g_sdk_shm_ptr == MAP_FAILED || g_sdk_shm_ptr == NULL) {
        fprintf(stderr, "SDK: Failed to create or map shared memory.\n");
        return -1;
    }

    g_master = ecrt_request_master({{ config.masterIndex | default(0) }});
    if (!g_master) {
        fprintf(stderr, "SDK: Failed to request master.\n");
        internal_cleanup_shm();
        return -2;
    }

    g_domain1 = ecrt_master_create_domain(g_master);
    if (!g_domain1) {
        fprintf(stderr, "SDK: Failed to create domain.\n");
        ecrt_release_master(g_master);
        g_master = NULL;
        internal_cleanup_shm();
        return -3;
    }

    // Configure slaves
    {% for slave in config.slaves %}
    g_sc_slave{{ slave.slave_index }} = ecrt_master_slave_config(g_master, SLAVE{{ slave.slave_index }}_POS, SLAVE{{ slave.slave_index }}_VID_PID);
    if (!g_sc_slave{{ slave.slave_index }}) {
        fprintf(stderr, "SDK: Failed to get slave {{ slave.slave_index }} configuration.\n");
        ecrt_release_master(g_master);
        g_master = NULL;
        internal_cleanup_shm();
        return -4;
    }
    
    // Configure SDOs
    {% if slave.sdos %}
    fprintf(stdout, "SDK: Configuring {{ slave.sdos | length }} SDOs for slave {{ slave.slave_index }}...\n");
    {% for sdo in slave.sdos %}
    if (ecrt_slave_config_sdo{{ sdo.sdo_func_suffix }}(g_sc_slave{{ slave.slave_index }}, {{ sdo.index }}, {{ sdo.subindex }}, {{ sdo.value }})) {
        fprintf(stderr, "SDK: Failed to configure SDO {{ sdo.name }} ({{ sdo.index }}:{{ sdo.subindex }}) on slave {{ slave.slave_index }}.\n");
        ecrt_release_master(g_master);
        g_master = NULL;
        internal_cleanup_shm();
        return -1;
    }
    {% endfor %}
    {% endif %}

    // Configure PDOs with pthread
    pthread_t pdo_config_thread_{{slave.slave_index}};
    int pdo_config_result_{{slave.slave_index}} = 0;
    config_thread_param_t pdo_param_{{slave.slave_index}};
    pdo_param_{{slave.slave_index}}.slave_config = g_sc_slave{{ slave.slave_index }};
    pdo_param_{{slave.slave_index}}.sync_info = slave{{ slave.slave_index }}_syncs;
    pdo_param_{{slave.slave_index}}.result = &pdo_config_result_{{slave.slave_index}};

    if (pthread_create(&pdo_config_thread_{{slave.slave_index}}, NULL, config_slave_thread_func, &pdo_param_{{slave.slave_index}})) {
        fprintf(stderr, "SDK: Failed to create PDO config thread for slave {{ slave.slave_index }}.\n");
        ecrt_release_master(g_master);
        g_master = NULL;
        internal_cleanup_shm();
        return -5;
    }
    pthread_join(pdo_config_thread_{{slave.slave_index}}, NULL);
    if (pdo_config_result_{{slave.slave_index}} != 0) {
        fprintf(stderr, "SDK: Failed to configure PDOs for slave {{ slave.slave_index }} (result: %d).\n", pdo_config_result_{{slave.slave_index}});
        ecrt_release_master(g_master);
        g_master = NULL;
        internal_cleanup_shm();
        return -6;
    }
    fprintf(stdout, "SDK: PDOs configured for slave {{ slave.slave_index }}.\n");
    
    // Configure DC with pthread
    {% if slave.dc_config %}
    pthread_t dc_config_thread_{{slave.slave_index}};
    int dc_config_result_{{slave.slave_index}} = 0;
    dc_config_thread_param_t dc_param_{{slave.slave_index}};
    dc_param_{{slave.slave_index}}.slave_config = g_sc_slave{{ slave.slave_index }};
    dc_param_{{slave.slave_index}}.assign_activate = {{ slave.dc_config.assign_activate }};
    dc_param_{{slave.slave_index}}.sync0_cycle = {{ slave.dc_config.sync0_cycle }};
    dc_param_{{slave.slave_index}}.sync0_shift = {{ slave.dc_config.sync0_shift }};
    dc_param_{{slave.slave_index}}.sync1_cycle = {{ slave.dc_config.sync1_cycle }};
    dc_param_{{slave.slave_index}}.sync1_shift = {{ slave.dc_config.sync1_shift }};
    dc_param_{{slave.slave_index}}.result = &dc_config_result_{{slave.slave_index}};

    if (pthread_create(&dc_config_thread_{{slave.slave_index}}, NULL, dc_config_thread_func, &dc_param_{{slave.slave_index}})) {
        fprintf(stderr, "SDK: Failed to create DC config thread for slave {{ slave.slave_index }}.\n");
        ecrt_release_master(g_master);
        g_master = NULL;
        internal_cleanup_shm();
        return -7;
    }
    pthread_join(dc_config_thread_{{slave.slave_index}}, NULL);
    if (dc_config_result_{{slave.slave_index}} != 0) {
        fprintf(stderr, "SDK: Failed to configure DC for slave {{ slave.slave_index }} (result: %d).\n", dc_config_result_{{slave.slave_index}});
        ecrt_release_master(g_master);
        g_master = NULL;
        internal_cleanup_shm();
        return -8;
    }
     fprintf(stdout, "SDK: DC configured for slave {{ slave.slave_index }}.\n");
    {% endif %}
    {% endfor %}

    if (ecrt_domain_reg_pdo_entry_list(g_domain1, domain1_regs)) {
        fprintf(stderr, "SDK: PDO entry registration failed.\n");
        ecrt_release_master(g_master);
        g_master = NULL;
        internal_cleanup_shm();
        return -9;
    }
    fprintf(stdout, "SDK: PDO entries registered to domain.\n");

    fprintf(stdout, "SDK: Activating master...\n");
    if (ecrt_master_activate(g_master)) {
        fprintf(stderr, "SDK: Master activation failed.\n");
        ecrt_release_master(g_master);
        g_master = NULL;
        internal_cleanup_shm();
        return -10;
    }

    if (!(g_domain1_pd = ecrt_domain_data(g_domain1))) {
        fprintf(stderr, "SDK: Failed to get domain process data.\n");
        ecrt_release_master(g_master);
        g_master = NULL;
        internal_cleanup_shm();
        return -11;
    }
    
    fprintf(stdout, "SDK: Initialization successful.\n");
    return 0;
}

__attribute__((visibility("default"))) int sdk_start_cyclic_task() {
    if (!g_master || !g_domain1_pd) {
        fprintf(stderr, "SDK: Cannot start cyclic task, SDK not initialized or master not active.\n");
        return -1;
    }
    if (g_run_cyclic_task) {
        fprintf(stderr, "SDK: Cyclic task already running.\n");
        return -2;
    }

    g_run_cyclic_task = 1;
    if (pthread_create(&g_cyclic_task_thread_id, NULL, cyclic_task_main, NULL) != 0) {
        perror("SDK: Failed to create cyclic task thread");
        g_run_cyclic_task = 0;
        return -3;
    }
    fprintf(stdout, "SDK: Cyclic task started.\n");
    return 0;
}

__attribute__((visibility("default"))) void sdk_stop_cyclic_task() {
    if (g_run_cyclic_task) {
        g_run_cyclic_task = 0;
        if (pthread_join(g_cyclic_task_thread_id, NULL)) {
            perror("SDK: Failed to join cyclic task thread");
        }
         fprintf(stdout, "SDK: Cyclic task stopped.\n");
    } else {
         fprintf(stdout, "SDK: Cyclic task was not running.\n");
    }
}

__attribute__((visibility("default"))) void sdk_cleanup() {
    sdk_stop_cyclic_task();

    if (g_master) {
        fprintf(stdout, "SDK: Deactivating master...\n");
        ecrt_master_deactivate(g_master);
        fprintf(stdout, "SDK: Releasing master...\n");
        ecrt_release_master(g_master);
        g_master = NULL;
    }
    
    internal_cleanup_shm();
    g_domain1_pd = NULL;
    g_domain1 = NULL; 
    {% for slave in config.slaves %}
    g_sc_slave{{ slave.slave_index }} = NULL;
    {% endfor %}

    if (munlockall() == -1) {
        perror("SDK: munlockall() failed");
    }

    fprintf(stdout, "SDK: Cleanup complete.\n");
}

__attribute__((visibility("default"))) int sdk_write_slave(uint16_t slave_number, uint16_t index, uint8_t subindex, int32_t value) {
    if (!g_sdk_shm_ptr || g_sdk_shm_ptr == MAP_FAILED) {
        fprintf(stderr, "SDK: SHM not available for write.\n");
        return -1;
    }
    int result = 0;
    pthread_mutex_lock(&g_shm_mutex);

    switch(slave_number) {
    {% for slave in config.slaves %}
    case {{ slave.slave_index }}:
        switch (index) {
{{ slave.generated_c.sdk_write_cases | indent(12, true) }}
            default:
                result = -2; // Invalid index for write
                break;
        }
        break;
    {% endfor %}
    default:
        result = -4; // Invalid slave number
        break;
    }

    pthread_mutex_unlock(&g_shm_mutex);
    if (result != 0) {
        fprintf(stderr, "SDK: sdk_write_slave failed (slave:%u, idx:0x%X, sub:0x%X, err:%d)\n",
                slave_number, index, subindex, result);
    }
    return result;
}

__attribute__((visibility("default"))) int sdk_read_slave(uint16_t slave_number, uint16_t index, uint8_t subindex, int32_t *value_ptr) {
    if (!g_sdk_shm_ptr || g_sdk_shm_ptr == MAP_FAILED) {
        fprintf(stderr, "SDK: SHM not available for read.\n");
        return -1;
    }
    if (value_ptr == NULL) return -5; // Null pointer

    int result = 0;
    pthread_mutex_lock(&g_shm_mutex);

    switch(slave_number) {
    {% for slave in config.slaves %}
    case {{ slave.slave_index }}:
        switch (index) {
{{ slave.generated_c.sdk_read_cases | indent(12, true) }}
            default:
                result = -2; // Invalid index for read
                break;
        }
        break;
    {% endfor %}
    default:
        result = -4; // Invalid slave number
        break;
    }

    pthread_mutex_unlock(&g_shm_mutex);
    if (result != 0) {
        fprintf(stderr, "SDK: sdk_read_slave failed (slave:%u, idx:0x%X, sub:0x%X, err:%d)\n",
                slave_number, index, subindex, result);
    }
    return result;
}

__attribute__((visibility("default"))) int sdk_get_slave_status(uint16_t slave_number, int32_t* online_status, int32_t* op_status, int32_t* al_state) {
    if (!g_sdk_shm_ptr || g_sdk_shm_ptr == MAP_FAILED) return -1;
    if (!online_status || !op_status || !al_state) return -5; // Null pointers

    int result = 0;
    pthread_mutex_lock(&g_shm_mutex);

    switch (slave_number) {
    {% for slave in config.slaves %}
    case {{ slave.slave_index }}:
        *online_status = g_sdk_shm_ptr->shm_slave{{ slave.slave_index }}_online_status;
        *op_status = g_sdk_shm_ptr->shm_slave{{ slave.slave_index }}_operational_status;
        *al_state = g_sdk_shm_ptr->shm_slave{{ slave.slave_index }}_al_state;
        break;
    {% endfor %}
    default:
        result = -4; // Invalid slave number
        break;
    }
    
    pthread_mutex_unlock(&g_shm_mutex);
    if (result != 0) {
         fprintf(stderr, "SDK: sdk_get_slave_status failed for slave %u (err:%d)\n", slave_number, result);
    }
    return result;
}

__attribute__((visibility("default"))) int sdk_connect() {
    internal_create_shm();
    if (g_sdk_shm_ptr == MAP_FAILED || g_sdk_shm_ptr == NULL) {
        fprintf(stderr, "SDK: User Failed to create or map shared memory.\n");
        return -1; // SHM is critical
    }
    return 0;
}
