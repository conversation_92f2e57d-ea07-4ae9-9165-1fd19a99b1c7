#ifndef SDK_H
#define SDK_H

#include <stdint.h>

#ifdef __cplusplus
extern "C" {
#endif

// SDK API Functions
__attribute__((visibility("default"))) int sdk_init();
__attribute__((visibility("default"))) int sdk_start_cyclic_task();
__attribute__((visibility("default"))) void sdk_stop_cyclic_task();
__attribute__((visibility("default"))) void sdk_cleanup();
__attribute__((visibility("default"))) int sdk_write_slave(uint16_t slave_number, uint16_t index, uint8_t subindex, int32_t value);
__attribute__((visibility("default"))) int sdk_read_slave(uint16_t slave_number, uint16_t index, uint8_t subindex, int32_t* value_ptr);
__attribute__((visibility("default"))) int sdk_get_slave_status(uint16_t slave_number, int32_t* online_status, int32_t* op_status, int32_t* al_state);
__attribute__((visibility("default"))) int sdk_connect();

#ifdef __cplusplus
}
#endif

#endif // SDK_H 