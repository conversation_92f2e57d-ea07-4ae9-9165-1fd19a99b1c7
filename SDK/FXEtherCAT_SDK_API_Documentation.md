# FXEthercatSDK C# 封装库 API 文档

**版本**: 1.0.0.1
**最后更新日期**: 2025年6月24日

---

## 1. 概述

本文档旨在为使用 `FXEthercatSDK` 的 .NET 开发者提供详细的 API 参考。

`SDKWrapper.cs` 文件定义了 `FXEtherCAT.SDK` 命名空间，其核心是 `FXEtherCATSDK` 类。这个类作为 C# 应用程序与底层FXEtherCAT主站之间的桥梁。

### 主要作用

*   **简化集成**: 开发者无需关心底层的实现细节，可以直接使用高级的 C# 方法与 FXEtherCAT 主站进行交互。
*   **资源管理**: 通过实现 `IDisposable` 接口，该类可以与 `using` 语句结合使用，自动管理底层非托管资源的分配与释放，有效防止内存和句柄泄漏。
*   **类型安全与错误处理**: 将 C 库返回的整数错误码转换为 C# 的布尔值和自定义异常，使代码更健壮、错误处理更直观。

## 2. API 参考

### 2.1. 命名空间: `FXEtherCAT.SDK`

| 名称                 | 描述                                                       |
| -------------------- | ---------------------------------------------------------- |
| `FXEtherCATSDK`          | **核心类**，封装了所有与 SDK 的交互逻辑。                |
| `SlaveDeviceStatus`    | **结构体**，用于表示 FXEtherCAT 从站设备的状态。             |
| `SDKInitializationException` | **异常类**，在 SDK 初始化或连接失败时抛出。            |

---

### 2.2. `SlaveDeviceStatus` 结构体

该结构体用于封装从站的状态信息。

**公共属性:**

*   `public bool IsOnline { get; }`
    *   获取一个值，该值指示从站当前是否在线并与主站通信。
*   `public bool IsOperational { get; }`
    *   获取一个值，该值指示从站是否处于 `Operational` (OP) 状态，这是进行过程数据交换的必要条件。
*   `public int AlState { get; }`
    *   获取从站原始的 AL (Application Layer) 状态码。

---

### 2.3. `FXEtherCATSDK` 类

#### **公共方法**

*   `public bool WriteSlave(ushort slaveNumber, ushort index, byte subindex, int value)`
    *   **描述**: 向从站中写入一个32位整数值。此数据旨在通过 PDO 发送到目标从站，通常用于设置控制字、目标速度等（RxPDO）。
    *   **参数**:
        *   `slaveNumber` (ushort): 目标从站的逻辑地址（例如 0）。
        *   `index` (ushort): **对象字典 (Object Dictionary, OD) 的索引**。在 EtherCAT (特别是 CoE 协议) 中，从站的所有可访问参数都组织在一个称为对象字典的表格中。`index` 是这个表格中的主地址，通常以十六进制表示（例如 `0x6040`）。**开发者必须查阅具体从站设备的官方文档（例如 ESI 文件或用户手册）来获取正确的索引值。**
        *   `subindex` (byte): **对象字典的子索引**。一个 `index` 常常可以包含多个子参数，`subindex` 用于访问其中的某一个。
            *   对于简单的参数（例如状态字 `0x6041`），其值通常存储在 `subindex 0`。
            *   对于更复杂的、数组或记录类型的参数，不同的子索引会对应不同的值。例如，一个对象可能用 `subindex 1` 表示最小值，`subindex 2` 表示最大值。
            *   **与 `index` 一样，正确的 `subindex` 值也必须从设备文档中获取。**
        *   `value` (int): 要写入的32位整数值。
    *   **返回值**: `true` 表示成功写入从站；`false` 表示失败。

*   `public bool ReadSlave(ushort slaveNumber, ushort index, byte subindex, out int value)`
    *   **描述**: 从从站中读取一个32位整数值。此数据是由从站通过 PDO 发送到主站的，通常用于获取状态字、实际速度等（TxPDO）。
    *   **参数**:
        *   `slaveNumber`, `index`, `subindex`: 含义与 `WriteSlave` 方法中的参数完全相同。请参考上面的详细描述。
        *   `value` (out int): 如果读取成功，该参数将包含从从站中读取到的值。
    *   **返回值**: `true` 表示成功读取；`false` 表示失败。

*   `public bool GetSlaveDeviceStatus(ushort slaveNumber, out SlaveDeviceStatus status)`
    *   **描述**: 获取指定从站的当前状态，包括是否在线、是否在 OP 模式以及 AL 状态码。
    *   **参数**:
        *   `slaveNumber` (ushort): 目标从站的逻辑地址。
        *   `status` (out SlaveDeviceStatus): 如果成功，该参数将包含从站的状态信息。
    *   **返回值**: `true` 表示状态获取成功；`false` 表示失败。

*   `public bool IsInitialized()`
    *   **描述**: 获取一个布尔值，该值指示 SDK 是否已成功初始化（即 `sdk_connect` 是否成功）。

---

## 3. 使用示例

以下代码片段展示了如何在一个典型的 C# 客户端应用程序中使用 `FXEtherCATSDK` 类。

```csharp
using FXEtherCAT.SDK;
using System;
using System.Threading;

public class FXEtherCATClient
{
    public static void Main()
    {
        Console.WriteLine("启动 FXEtherCAT 客户端...");

        try
        {
            // 使用 'using' 语句可以确保 Dispose() 方法在代码块结束时被自动调用，
            // 从而安全地释放底层资源。
            using (var sdk = new FXEtherCATSDK())
            {
                Console.WriteLine("成功连接到 FXEtherCAT SDK 的从站。");

                // 循环读取从站状态和数据
                for (int i = 0; i < 5; i++)
                {
                    if (sdk.GetSlaveDeviceStatus(0, out SlaveDeviceStatus status))
                    {
                        Console.WriteLine($"[从站 0 状态] Online: {status.IsOnline}, Operational: {status.IsOperational}, AL-State: 0x{status.AlState:X2}");

                        if (status.IsOperational)
                        {
                            // 尝试读取状态字 (0x6041)
                            if (sdk.ReadSlave(0, 0x6041, 0, out int statusWord))
                            {
                                Console.WriteLine($"  读取到状态字: 0x{statusWord:X4}");
                            }

                            // 尝试写入控制字 (0x6040)
                            if (sdk.WriteSlave(0, 0x6040, 0, 0x0F))
                            {
                                Console.WriteLine("  成功写入控制字: 0x0F");
                            }
                        }
                    }
                    else
                    {
                        Console.WriteLine("获取从站 0 状态失败。");
                    }
                    Thread.Sleep(1000); // 等待1秒
                }
            } // sdk.Dispose() 会在此处被自动调用
        }
        catch (SDKInitializationException ex)
        {
            Console.WriteLine($"错误: 无法连接到 SDK。");
            Console.WriteLine($"  > {ex.Message}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"发生意外错误: {ex.Message}");
        }

        Console.WriteLine("客户端程序执行完毕。");
    }
}
```