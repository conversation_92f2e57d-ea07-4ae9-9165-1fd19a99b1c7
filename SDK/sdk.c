#define _GNU_SOURCE
#include "sdk.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <pthread.h>
#include <unistd.h> // For sleep, usleep
#include <sys/mman.h>
#include <fcntl.h>
#include <sched.h>
#include <sys/stat.h>
#include <time.h>   // For clock_gettime, CLOCK_MONOTONIC, etc.
#include <errno.h>  // For errno

#include "ecrt.h" // EtherCAT master library

// --- Internal SDK Configuration and State ---

// Time definitions
#define NSEC_PER_SEC (1000000000L)
#define TIMESPEC2NS(T) ((uint64_t) (T).tv_sec * NSEC_PER_SEC + (T).tv_nsec)
#define TASK_FREQUENCY 4000 // Hz
#define PERIOD_NS (NSEC_PER_SEC / TASK_FREQUENCY)
#define STATUS_UPDATE_INTERVAL_MS 1000
#define STATUS_UPDATE_PERIOD ((TASK_FREQUENCY * STATUS_UPDATE_INTERVAL_MS) / 1000)


// PDO/DC configuration thread parameters
typedef struct {
    ec_slave_config_t *slave_config;
    ec_sync_info_t *sync_info;
    int *result;
} config_thread_param_t;

typedef struct {
    ec_slave_config_t *slave_config;
    uint16_t assign_activate;
    uint32_t sync0_cycle;
    uint32_t sync0_shift;
    uint32_t sync1_cycle;
    uint32_t sync1_shift;
    int *result;
} dc_config_thread_param_t;

// Shared memory configuration
#define INTERNAL_SHM_FILE "qb3tau3h4rlo2tc_L5N_Test_shm_sdk_shm_sdk"

// Internal representation of shared memory data
typedef struct {
    int32_t shm_slave0_online_status;
    int32_t shm_slave0_operational_status;
    int32_t shm_slave0_al_state;
    int32_t shm_slave0_rx_0x00007100_outbyte0;
    int32_t shm_slave0_rx_0x00007100_outbyte1;
    int32_t shm_slave0_tx_0x00006000_inbyte0;
    int32_t shm_slave0_tx_0x00006000_inbyte1;
    int32_t shm_slave0_tx_0x00008002_module_state;
    int32_t shm_slave0_tx_0x00008003_module_err_num;
    int32_t shm_slave0_tx_0x00008102_module_state;
    int32_t shm_slave0_tx_0x00008103_module_err_num;
} sdk_internal_shm_data_t;

static sdk_internal_shm_data_t *g_sdk_shm_ptr = NULL;
static size_t g_sdk_shm_size = sizeof(sdk_internal_shm_data_t);
static char g_shm_name[256] = INTERNAL_SHM_FILE;

// EtherCAT master and domain data
static ec_master_t *g_master = NULL;
static ec_master_state_t g_master_state = {};
static ec_domain_t *g_domain1 = NULL;
static ec_domain_state_t g_domain1_state = {};
static uint8_t *g_domain1_pd = NULL; // Process data

// Slave configuration data
static ec_slave_config_t *g_sc_slave0 = NULL;
static ec_slave_config_state_t g_sc_slave0_state = {};

// Cyclic task control
static volatile int g_run_cyclic_task = 0;
static pthread_t g_cyclic_task_thread_id;
static pthread_mutex_t g_shm_mutex = PTHREAD_MUTEX_INITIALIZER;
static int g_core_affinity = -1; // -1 means no affinity set

// PDO entry offsets
static struct {
    unsigned int pdo_slave0_rx_0x00007100_outbyte0;
    unsigned int pdo_slave0_rx_0x00007100_outbyte1;
    unsigned int pdo_slave0_tx_0x00006000_inbyte0;
    unsigned int pdo_slave0_tx_0x00006000_inbyte1;
    unsigned int pdo_slave0_tx_0x00008002_module_state;
    unsigned int pdo_slave0_tx_0x00008003_module_err_num;
    unsigned int pdo_slave0_tx_0x00008102_module_state;
    unsigned int pdo_slave0_tx_0x00008103_module_err_num;
} g_pdo_offsets;

// PDO registration list
#define SLAVE0_POS 0,0
#define SLAVE0_VID_PID 0x00000099,0x00020310
const static ec_pdo_entry_reg_t domain1_regs[] = {
    {SLAVE0_POS, SLAVE0_VID_PID, 0x00007100, 1, &g_pdo_offsets.pdo_slave0_rx_0x00007100_outbyte0},
    {SLAVE0_POS, SLAVE0_VID_PID, 0x00007100, 2, &g_pdo_offsets.pdo_slave0_rx_0x00007100_outbyte1},
    {SLAVE0_POS, SLAVE0_VID_PID, 0x00006000, 1, &g_pdo_offsets.pdo_slave0_tx_0x00006000_inbyte0},
    {SLAVE0_POS, SLAVE0_VID_PID, 0x00006000, 2, &g_pdo_offsets.pdo_slave0_tx_0x00006000_inbyte1},
    {SLAVE0_POS, SLAVE0_VID_PID, 0x00008002, 0, &g_pdo_offsets.pdo_slave0_tx_0x00008002_module_state},
    {SLAVE0_POS, SLAVE0_VID_PID, 0x00008003, 0, &g_pdo_offsets.pdo_slave0_tx_0x00008003_module_err_num},
    {SLAVE0_POS, SLAVE0_VID_PID, 0x00008102, 0, &g_pdo_offsets.pdo_slave0_tx_0x00008102_module_state},
    {SLAVE0_POS, SLAVE0_VID_PID, 0x00008103, 0, &g_pdo_offsets.pdo_slave0_tx_0x00008103_module_err_num},
    {}
};

// PDO entries for slave 0
static ec_pdo_entry_info_t slave0_pdo_entries[] = {
    {0x00007100, 1, 8},  /* OutByte0 */
    {0x00007100, 2, 8},  /* OutByte1 */
    {0x00006000, 1, 8},  /* InByte0 */
    {0x00006000, 2, 8},  /* InByte1 */
    {0x00008002, 0, 16},  /* Module State */
    {0x00008003, 0, 32},  /* Module Err Num */
    {0x00008102, 0, 16},  /* Module State */
    {0x00008103, 0, 32},  /* Module Err Num */
};

// PDO info for slave 0
static ec_pdo_info_t slave0_pdos[] = {
    {0x00001601, 2, slave0_pdo_entries + 0},  /* RxPDO */
    {0x00001a00, 0x00001a01, 6, slave0_pdo_entries + 2},  /* TxPDO */
};

// Sync manager info for slave 0
static ec_sync_info_t slave0_syncs[] = {
    {0, EC_DIR_OUTPUT, 0, NULL, EC_WD_DISABLE},
    {1, EC_DIR_INPUT, 0, NULL, EC_WD_DISABLE},
    {2, EC_DIR_OUTPUT, 1, slave0_pdos + 0, EC_WD_ENABLE},
    {3, EC_DIR_INPUT, 1, slave0_pdos + 1, EC_WD_DISABLE},
    {0xff}
};

// --- Helper Functions ---

static void* config_slave_thread_func(void *arg) {
    config_thread_param_t *param = (config_thread_param_t *)arg;
    *(param->result) = ecrt_slave_config_pdos(param->slave_config, EC_END, param->sync_info);
    return NULL;
}

static void* dc_config_thread_func(void *arg) {
    dc_config_thread_param_t *param = (dc_config_thread_param_t *)arg;
    int ret = ecrt_slave_config_dc(
        param->slave_config,
        param->assign_activate,
        param->sync0_cycle,
        param->sync0_shift,
        param->sync1_cycle,
        param->sync1_shift
    );
    *(param->result) = ret;
    return NULL;
}

static void internal_create_shm() {
    int fd = shm_open(g_shm_name, O_CREAT | O_RDWR, 0666);
    if (fd < 0) {
        perror("SDK: shm_open failed");
        g_sdk_shm_ptr = MAP_FAILED;
        return;
    }
    if (ftruncate(fd, g_sdk_shm_size) < 0) {
        perror("SDK: ftruncate failed");
        close(fd);
        shm_unlink(g_shm_name);
        g_sdk_shm_ptr = MAP_FAILED;
        return;
    }
    g_sdk_shm_ptr = (sdk_internal_shm_data_t *)mmap(NULL, g_sdk_shm_size,
                                                 PROT_READ | PROT_WRITE,
                                                 MAP_SHARED, fd, 0);
    if (g_sdk_shm_ptr == MAP_FAILED) {
        perror("SDK: mmap failed");
        close(fd);
        shm_unlink(g_shm_name);
        return;
    }
    close(fd);
    memset(g_sdk_shm_ptr, 0, g_sdk_shm_size);
    fprintf(stdout, "SDK: Shared memory '%s' created/opened successfully.\n", g_shm_name);
}

static void internal_cleanup_shm() {
    if (g_sdk_shm_ptr != NULL && g_sdk_shm_ptr != MAP_FAILED) {
        if (munmap(g_sdk_shm_ptr, g_sdk_shm_size) == -1) {
            perror("SDK: munmap failed");
        }
        g_sdk_shm_ptr = NULL; 
        if (shm_unlink(g_shm_name) == -1) {
            if (errno != ENOENT) {
                perror("SDK: shm_unlink failed");
            }
        }
        fprintf(stdout, "SDK: Shared memory '%s' unmapped and unlinked.\n", g_shm_name);
    } else {
        shm_unlink(g_shm_name);
    }
}

static void timespec_add_ns(struct timespec *ts, uint64_t ns) {
    ts->tv_nsec += ns;
    while (ts->tv_nsec >= NSEC_PER_SEC) {
        ts->tv_nsec -= NSEC_PER_SEC;
        ts->tv_sec++;
    }
}

// --- Cyclic Task ---
static void *cyclic_task_main(void *arg) {
    (void)arg; // Unused
    struct timespec wakeup_time;
    int cycle_counter = 0;

    // Set CPU affinity if requested
    if (g_core_affinity != -1) {
        cpu_set_t cpuset;
        CPU_ZERO(&cpuset);
        CPU_SET(g_core_affinity, &cpuset);
        if (pthread_setaffinity_np(pthread_self(), sizeof(cpu_set_t), &cpuset) != 0) {
            perror("SDK: pthread_setaffinity_np failed");
        } else {
            fprintf(stdout, "SDK: Cyclic task pinned to CPU core %d\n", g_core_affinity);
        }
    }
    
    // Set real-time priority for this thread
    struct sched_param sparam;
    sparam.sched_priority = sched_get_priority_max(SCHED_FIFO);
    if (sched_setscheduler(0, SCHED_FIFO, &sparam) == -1) {
        perror("SDK: sched_setscheduler for cyclic task failed (run with sudo?)");
    } else {
         fprintf(stdout, "SDK: Cyclic task using SCHED_FIFO priority %d.\n", sparam.sched_priority);
    }

    clock_gettime(CLOCK_MONOTONIC, &wakeup_time);

    while (g_run_cyclic_task) {
        timespec_add_ns(&wakeup_time, PERIOD_NS);
        clock_nanosleep(CLOCK_MONOTONIC, TIMER_ABSTIME, &wakeup_time, NULL);

        ecrt_master_application_time(g_master, TIMESPEC2NS(wakeup_time));
        ecrt_master_receive(g_master);
        
        ecrt_domain_process(g_domain1);

        pthread_mutex_lock(&g_shm_mutex);

        if (g_sdk_shm_ptr) { // Check if SHM is valid
            if (cycle_counter > 0) {
                cycle_counter--;
            } else {
                cycle_counter = STATUS_UPDATE_PERIOD;
                ecrt_slave_config_state(g_sc_slave0, &g_sc_slave0_state);
                g_sdk_shm_ptr->shm_slave0_online_status = g_sc_slave0_state.online;
                g_sdk_shm_ptr->shm_slave0_operational_status = g_sc_slave0_state.operational;
                g_sdk_shm_ptr->shm_slave0_al_state = g_sc_slave0_state.al_state;
            }

            if (g_domain1_pd) { // Check if PD is valid
                // Read TxPDO data from EtherCAT domain to SHM
                g_sdk_shm_ptr->shm_slave0_tx_0x00006000_inbyte0 = EC_READ_U8(g_domain1_pd + g_pdo_offsets.pdo_slave0_tx_0x00006000_inbyte0);
                g_sdk_shm_ptr->shm_slave0_tx_0x00006000_inbyte1 = EC_READ_U8(g_domain1_pd + g_pdo_offsets.pdo_slave0_tx_0x00006000_inbyte1);
                g_sdk_shm_ptr->shm_slave0_tx_0x00008002_module_state = EC_READ_U16(g_domain1_pd + g_pdo_offsets.pdo_slave0_tx_0x00008002_module_state);
                g_sdk_shm_ptr->shm_slave0_tx_0x00008003_module_err_num = EC_READ_U32(g_domain1_pd + g_pdo_offsets.pdo_slave0_tx_0x00008003_module_err_num);
                g_sdk_shm_ptr->shm_slave0_tx_0x00008102_module_state = EC_READ_U16(g_domain1_pd + g_pdo_offsets.pdo_slave0_tx_0x00008102_module_state);
                g_sdk_shm_ptr->shm_slave0_tx_0x00008103_module_err_num = EC_READ_U32(g_domain1_pd + g_pdo_offsets.pdo_slave0_tx_0x00008103_module_err_num);

                // Write RxPDO data from SHM to EtherCAT domain
                EC_WRITE_U8(g_domain1_pd + g_pdo_offsets.pdo_slave0_rx_0x00007100_outbyte0, g_sdk_shm_ptr->shm_slave0_rx_0x00007100_outbyte0);
                EC_WRITE_U8(g_domain1_pd + g_pdo_offsets.pdo_slave0_rx_0x00007100_outbyte1, g_sdk_shm_ptr->shm_slave0_rx_0x00007100_outbyte1);
            }
        }
        
        pthread_mutex_unlock(&g_shm_mutex);

        ecrt_domain_queue(g_domain1);
        ecrt_master_sync_slave_clocks(g_master);
        ecrt_master_sync_reference_clock(g_master);
        ecrt_master_send(g_master);
    }
    fprintf(stdout, "SDK: Cyclic task finished.\n");
    return NULL;
}


// --- Public SDK API Functions ---

__attribute__((visibility("default"))) int sdk_init() {
    fprintf(stdout, "SDK: Initializing...\n");
    
    if (mlockall(MCL_CURRENT | MCL_FUTURE) == -1) {
        perror("SDK: mlockall failed");
    }

    internal_create_shm();
    if (g_sdk_shm_ptr == MAP_FAILED || g_sdk_shm_ptr == NULL) {
        fprintf(stderr, "SDK: Failed to create or map shared memory.\n");
        return -1;
    }

    g_master = ecrt_request_master(0);
    if (!g_master) {
        fprintf(stderr, "SDK: Failed to request master.\n");
        internal_cleanup_shm();
        return -2;
    }

    g_domain1 = ecrt_master_create_domain(g_master);
    if (!g_domain1) {
        fprintf(stderr, "SDK: Failed to create domain.\n");
        ecrt_release_master(g_master);
        g_master = NULL;
        internal_cleanup_shm();
        return -3;
    }

    // Configure slaves
    g_sc_slave0 = ecrt_master_slave_config(g_master, SLAVE0_POS, SLAVE0_VID_PID);
    if (!g_sc_slave0) {
        fprintf(stderr, "SDK: Failed to get slave 0 configuration.\n");
        ecrt_release_master(g_master);
        g_master = NULL;
        internal_cleanup_shm();
        return -4;
    }
    
    // Configure SDOs
    fprintf(stdout, "SDK: Configuring 8 SDOs for slave 0...\n");
    if (ecrt_slave_config_sdo32(g_sc_slave0, 0x8001, 1, 0x00000181)) {
        fprintf(stderr, "SDK: Failed to configure SDO Module Config 1 (0x8001:1) on slave 0.\n");
        ecrt_release_master(g_master);
        g_master = NULL;
        internal_cleanup_shm();
        return -1;
    }
    if (ecrt_slave_config_sdo8(g_sc_slave0, 0x6001, 1, 0x06)) {
        fprintf(stderr, "SDK: Failed to configure SDO Module Config 1 (0x6001:1) on slave 0.\n");
        ecrt_release_master(g_master);
        g_master = NULL;
        internal_cleanup_shm();
        return -1;
    }
    if (ecrt_slave_config_sdo8(g_sc_slave0, 0x6001, 2, 0x06)) {
        fprintf(stderr, "SDK: Failed to configure SDO Module Config 2 (0x6001:2) on slave 0.\n");
        ecrt_release_master(g_master);
        g_master = NULL;
        internal_cleanup_shm();
        return -1;
    }
    if (ecrt_slave_config_sdo8(g_sc_slave0, 0x6001, 3, 0x06)) {
        fprintf(stderr, "SDK: Failed to configure SDO Module Config 3 (0x6001:3) on slave 0.\n");
        ecrt_release_master(g_master);
        g_master = NULL;
        internal_cleanup_shm();
        return -1;
    }
    if (ecrt_slave_config_sdo8(g_sc_slave0, 0x6001, 4, 0x06)) {
        fprintf(stderr, "SDK: Failed to configure SDO Module Config 4 (0x6001:4) on slave 0.\n");
        ecrt_release_master(g_master);
        g_master = NULL;
        internal_cleanup_shm();
        return -1;
    }
    if (ecrt_slave_config_sdo32(g_sc_slave0, 0x8101, 1, 0x00000372)) {
        fprintf(stderr, "SDK: Failed to configure SDO Module Config 1 (0x8101:1) on slave 0.\n");
        ecrt_release_master(g_master);
        g_master = NULL;
        internal_cleanup_shm();
        return -1;
    }
    if (ecrt_slave_config_sdo8(g_sc_slave0, 0x7102, 1, 0xff)) {
        fprintf(stderr, "SDK: Failed to configure SDO Module Config 1 (0x7102:1) on slave 0.\n");
        ecrt_release_master(g_master);
        g_master = NULL;
        internal_cleanup_shm();
        return -1;
    }
    if (ecrt_slave_config_sdo8(g_sc_slave0, 0x7102, 2, 0xff)) {
        fprintf(stderr, "SDK: Failed to configure SDO Module Config 2 (0x7102:2) on slave 0.\n");
        ecrt_release_master(g_master);
        g_master = NULL;
        internal_cleanup_shm();
        return -1;
    }

    // Configure PDOs with pthread
    pthread_t pdo_config_thread_0;
    int pdo_config_result_0 = 0;
    config_thread_param_t pdo_param_0;
    pdo_param_0.slave_config = g_sc_slave0;
    pdo_param_0.sync_info = slave0_syncs;
    pdo_param_0.result = &pdo_config_result_0;

    if (pthread_create(&pdo_config_thread_0, NULL, config_slave_thread_func, &pdo_param_0)) {
        fprintf(stderr, "SDK: Failed to create PDO config thread for slave 0.\n");
        ecrt_release_master(g_master);
        g_master = NULL;
        internal_cleanup_shm();
        return -5;
    }
    pthread_join(pdo_config_thread_0, NULL);
    if (pdo_config_result_0 != 0) {
        fprintf(stderr, "SDK: Failed to configure PDOs for slave 0 (result: %d).\n", pdo_config_result_0);
        ecrt_release_master(g_master);
        g_master = NULL;
        internal_cleanup_shm();
        return -6;
    }
    fprintf(stdout, "SDK: PDOs configured for slave 0.\n");
    
    // Configure DC with pthread

    if (ecrt_domain_reg_pdo_entry_list(g_domain1, domain1_regs)) {
        fprintf(stderr, "SDK: PDO entry registration failed.\n");
        ecrt_release_master(g_master);
        g_master = NULL;
        internal_cleanup_shm();
        return -9;
    }
    fprintf(stdout, "SDK: PDO entries registered to domain.\n");

    fprintf(stdout, "SDK: Activating master...\n");
    if (ecrt_master_activate(g_master)) {
        fprintf(stderr, "SDK: Master activation failed.\n");
        ecrt_release_master(g_master);
        g_master = NULL;
        internal_cleanup_shm();
        return -10;
    }

    if (!(g_domain1_pd = ecrt_domain_data(g_domain1))) {
        fprintf(stderr, "SDK: Failed to get domain process data.\n");
        ecrt_release_master(g_master);
        g_master = NULL;
        internal_cleanup_shm();
        return -11;
    }
    
    fprintf(stdout, "SDK: Initialization successful.\n");
    return 0;
}

__attribute__((visibility("default"))) int sdk_start_cyclic_task() {
    if (!g_master || !g_domain1_pd) {
        fprintf(stderr, "SDK: Cannot start cyclic task, SDK not initialized or master not active.\n");
        return -1;
    }
    if (g_run_cyclic_task) {
        fprintf(stderr, "SDK: Cyclic task already running.\n");
        return -2;
    }

    g_run_cyclic_task = 1;
    if (pthread_create(&g_cyclic_task_thread_id, NULL, cyclic_task_main, NULL) != 0) {
        perror("SDK: Failed to create cyclic task thread");
        g_run_cyclic_task = 0;
        return -3;
    }
    fprintf(stdout, "SDK: Cyclic task started.\n");
    return 0;
}

__attribute__((visibility("default"))) void sdk_stop_cyclic_task() {
    if (g_run_cyclic_task) {
        g_run_cyclic_task = 0;
        if (pthread_join(g_cyclic_task_thread_id, NULL)) {
            perror("SDK: Failed to join cyclic task thread");
        }
         fprintf(stdout, "SDK: Cyclic task stopped.\n");
    } else {
         fprintf(stdout, "SDK: Cyclic task was not running.\n");
    }
}

__attribute__((visibility("default"))) void sdk_cleanup() {
    sdk_stop_cyclic_task();

    if (g_master) {
        fprintf(stdout, "SDK: Deactivating master...\n");
        ecrt_master_deactivate(g_master);
        fprintf(stdout, "SDK: Releasing master...\n");
        ecrt_release_master(g_master);
        g_master = NULL;
    }
    
    internal_cleanup_shm();
    g_domain1_pd = NULL;
    g_domain1 = NULL; 
    g_sc_slave0 = NULL;

    if (munlockall() == -1) {
        perror("SDK: munlockall() failed");
    }

    fprintf(stdout, "SDK: Cleanup complete.\n");
}

__attribute__((visibility("default"))) int sdk_write_slave(uint16_t slave_number, uint16_t index, uint8_t subindex, int32_t value) {
    if (!g_sdk_shm_ptr || g_sdk_shm_ptr == MAP_FAILED) {
        fprintf(stderr, "SDK: SHM not available for write.\n");
        return -1;
    }
    int result = 0;
    pthread_mutex_lock(&g_shm_mutex);

    switch(slave_number) {
    case 0:
        switch (index) {
            case 0x00007100:
                if (subindex == 1) {
                    g_sdk_shm_ptr->shm_slave0_rx_0x00007100_outbyte0 = value;
                } else if (subindex == 2) {
                    g_sdk_shm_ptr->shm_slave0_rx_0x00007100_outbyte1 = value;
                } else {
                    result = -3; // Invalid subindex
                }
                break;
            default:
                result = -2; // Invalid index for write
                break;
        }
        break;
    default:
        result = -4; // Invalid slave number
        break;
    }

    pthread_mutex_unlock(&g_shm_mutex);
    if (result != 0) {
        fprintf(stderr, "SDK: sdk_write_slave failed (slave:%u, idx:0x%X, sub:0x%X, err:%d)\n",
                slave_number, index, subindex, result);
    }
    return result;
}

__attribute__((visibility("default"))) int sdk_read_slave(uint16_t slave_number, uint16_t index, uint8_t subindex, int32_t *value_ptr) {
    if (!g_sdk_shm_ptr || g_sdk_shm_ptr == MAP_FAILED) {
        fprintf(stderr, "SDK: SHM not available for read.\n");
        return -1;
    }
    if (value_ptr == NULL) return -5; // Null pointer

    int result = 0;
    pthread_mutex_lock(&g_shm_mutex);

    switch(slave_number) {
    case 0:
        switch (index) {
            case 0x00006000:
                if (subindex == 1) {
                    *value_ptr = g_sdk_shm_ptr->shm_slave0_tx_0x00006000_inbyte0;
                } else if (subindex == 2) {
                    *value_ptr = g_sdk_shm_ptr->shm_slave0_tx_0x00006000_inbyte1;
                } else {
                    result = -3; // Invalid subindex
                }
                break;
            case 0x00008002:
                if (subindex == 0) {
                    *value_ptr = g_sdk_shm_ptr->shm_slave0_tx_0x00008002_module_state;
                } else {
                    result = -3; // Invalid subindex
                }
                break;
            case 0x00008003:
                if (subindex == 0) {
                    *value_ptr = g_sdk_shm_ptr->shm_slave0_tx_0x00008003_module_err_num;
                } else {
                    result = -3; // Invalid subindex
                }
                break;
            case 0x00008102:
                if (subindex == 0) {
                    *value_ptr = g_sdk_shm_ptr->shm_slave0_tx_0x00008102_module_state;
                } else {
                    result = -3; // Invalid subindex
                }
                break;
            case 0x00008103:
                if (subindex == 0) {
                    *value_ptr = g_sdk_shm_ptr->shm_slave0_tx_0x00008103_module_err_num;
                } else {
                    result = -3; // Invalid subindex
                }
                break;
            default:
                result = -2; // Invalid index for read
                break;
        }
        break;
    default:
        result = -4; // Invalid slave number
        break;
    }

    pthread_mutex_unlock(&g_shm_mutex);
    if (result != 0) {
        fprintf(stderr, "SDK: sdk_read_slave failed (slave:%u, idx:0x%X, sub:0x%X, err:%d)\n",
                slave_number, index, subindex, result);
    }
    return result;
}

__attribute__((visibility("default"))) int sdk_get_slave_status(uint16_t slave_number, int32_t* online_status, int32_t* op_status, int32_t* al_state) {
    if (!g_sdk_shm_ptr || g_sdk_shm_ptr == MAP_FAILED) return -1;
    if (!online_status || !op_status || !al_state) return -5; // Null pointers

    int result = 0;
    pthread_mutex_lock(&g_shm_mutex);

    switch (slave_number) {
    case 0:
        *online_status = g_sdk_shm_ptr->shm_slave0_online_status;
        *op_status = g_sdk_shm_ptr->shm_slave0_operational_status;
        *al_state = g_sdk_shm_ptr->shm_slave0_al_state;
        break;
    default:
        result = -4; // Invalid slave number
        break;
    }
    
    pthread_mutex_unlock(&g_shm_mutex);
    if (result != 0) {
         fprintf(stderr, "SDK: sdk_get_slave_status failed for slave %u (err:%d)\n", slave_number, result);
    }
    return result;
}

__attribute__((visibility("default"))) int sdk_connect() {
    internal_create_shm();
    if (g_sdk_shm_ptr == MAP_FAILED || g_sdk_shm_ptr == NULL) {
        fprintf(stderr, "SDK: User Failed to create or map shared memory.\n");
        return -1; // SHM is critical
    }
    return 0;
}
