const fs = require('fs');
const path = require('path');

// 简单的生成脚本
async function generateSDK() {
    try {
        console.log('正在编译TypeScript...');
        
        // 先编译TypeScript
        const { exec } = require('child_process');
        await new Promise((resolve, reject) => {
            exec('npx tsc sdk-generator.service.ts --target es2017 --module commonjs --skipLibCheck', (error, stdout, stderr) => {
                if (error) {
                    console.error('编译错误:', error);
                    reject(error);
                    return;
                }
                console.log('TypeScript编译完成');
                resolve();
            });
        });
        
        // 导入编译后的模块
        delete require.cache[require.resolve('./sdk-generator.service.js')];
        const { SDKGeneratorService } = require('./sdk-generator.service.js');
        
        // 配置文件路径
        const configPath = path.resolve(__dirname, '../Demo/HX_16I16O/HX_16I16O.json');
        
        console.log('正在生成SDK文件...');
        console.log('配置文件:', configPath);
        
        // 生成SDK文件
        const result = await SDKGeneratorService.generateSDKFiles(configPath);
        
        // 写入C文件
        const cFilePath = path.join(__dirname, 'sdk.c');
        fs.writeFileSync(cFilePath, result.cCode, 'utf8');
        console.log('✅ 已生成C文件:', cFilePath);
        
        // 写入H文件
        const hFilePath = path.join(__dirname, 'sdk.h');
        fs.writeFileSync(hFilePath, result.hCode, 'utf8');
        console.log('✅ 已生成H文件:', hFilePath);
        
        console.log('\n🎉 SDK文件生成完成！');
        
    } catch (error) {
        console.error('❌ 生成SDK文件时出错:', error.message);
        console.error(error.stack);
        process.exit(1);
    }
}

generateSDK();
