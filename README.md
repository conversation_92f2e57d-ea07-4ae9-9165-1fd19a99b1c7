# FXEthercatSDK: FXEtherCAT 主站 SDK 及应用套件

## 概述

FXEthercatSDK 是一个用于 FXEtherCAT 主站控制的软件开发工具包（SDK）。它旨在提供一套灵活、可配置的解决方案，使开发人员能够快速集成和控制基于 EtherCAT 总线的伺服驱动器和其他从站设备。

本项目的核心是 C 语言实现的 FXEtherCAT 主站逻辑库 (`sdk.c`)，并提供 C# 封装库 (`SDK/FXEtherCATSDKWrapper`) 和 C# 控制应用程序示例 (`Demo/Controller`)。

## 项目目标

*   **模块化设计**：将 EtherCAT 通信核心、共享内存管理、周期性任务执行封装到可重用的库中。
*   **跨语言支持**：通过 C# P/Invoke 封装，方便 .NET 平台开发者集成和使用。
*   **实时控制**：支持实时或近实时的周期性任务，用于与 EtherCAT 从站进行数据交换。
*   **易于集成**：提供清晰的 API 和示例代码，简化集成过程。
*   **代码生成**：通过解析 JSON 配置文件，自动生成 C 语言的 FXEtherCAT 核心逻辑代码，极大提升了对不同从站设备配置的适应性和开发效率。

## 项目结构

```
.
├── Demo
│   ├── Controller          # C# 控制台应用示例
│   └── HX_16I16O           # HX_16I16O 模块的 C# 控制示例
├── FXEtherCAT_SDK_API_Documentation.md # C API 函数文档
├── README.md
├── Reference                   # 参考文件（从站配置示例、旧代码等）
│   ├── HX_16I16O.json      # HX_16I16O 从站配置文件
│   └── LS_L5N.json         # L5N 伺服驱动器从站配置文件
├── SDK
│   ├── middleware_host.c   # C 语言中间件宿主程序
│   ├── sdk.c               # [自动生成] FXEtherCAT 主站核心逻辑
│   ├── sdk.h               # [自动生成] 核心逻辑头文件
│   ├── sdk-generator.service.ts # 基于 TypeScript 的 SDK 代码生成器核心逻辑
│   ├── simple-generate.js  # 运行代码生成器的脚本
│   ├── package.json        # Node.js 项目配置文件
│   ├── templates           # Nunjucks 模板，用于生成 C/H 文件
│   │   ├── sdk.c.njk
│   │   └── sdk.h.njk
│   └── FXEtherCATSDKWrapper
│       ├── FXEtherCATSDKWrapper.csproj   # C# 封装库项目
│       └── SDKWrapper.cs       # P/Invoke 封装逻辑
├── tsconfig.json               # TypeScript 编译器配置
```

## 架构

项目的整体架构如下图所示：

```mermaid
graph TD
    A["JSON 配置文件"] --> B["SDK/sdk-generator.service.ts (代码生成器)"];
    B --> C["SDK/sdk.c & sdk.h 源文件"];

    subgraph "核心库 (C)"
        C -- "编译" --> E["libsdk.so (核心动态库)"];
    end

    subgraph "C# 控制层"
        F["Demo/Controller/ (C# 控制应用)"] --> G["SDK/FXEtherCATSDKWrapper/ (C# 封装库)"];
    end

    subgraph "C 中间件层"
        H["SDK/middleware_host.c"] -- "链接" --> E;
        H -- "编译" --> I["middleware_host (可执行文件)"];
    end

    G -.->|P/Invoke| E;

    E <--> J["共享内存"];
    E <--> K["EtherCAT 总线"];
    K <--> L["EtherCAT 从站"];
```

**主要组件说明：**

1.  **JSON 配置文件 (例如 `Reference/LS_L5N.json`)**:
    *   定义 FXEtherCAT 主站的行为，包括从站列表、PDO 映射等。
    *   此文件是生成 `sdk.c` 的输入。

2.  **`SDK/sdk-generator.service.ts` (代码生成器)**:
    *   此工具负责解析 JSON 配置文件 (例如 `Reference/LS_L5N.json`)。
    *   基于 Nunjucks 模板 (`SDK/templates/`)，根据配置内容动态生成 `sdk.c` 和 `sdk.h` 的源代码。
    *   这是实现 SDK 高度灵活性和可配置性的关键。

3.  **`SDK/sdk.c` / `sdk.h` (C 核心库)**:
    *   **自动生成**: 这两个文件是由代码生成器根据 JSON 配置生成的，不应手动修改。
    *   `sdk.h`: 定义了导出的 C API 函数接口以及与共享内存对应的结构体。
    *   `sdk.c`: 包含了 FXEtherCAT 主站的全部核心逻辑。
    *   编译后生成 `libsdk.so` (Linux) 或 `sdk.dll` (Windows)。

4.  **`SDK/middleware_host.c` (C 中间件宿主程序)**:
    *   一个轻量级的 C 可执行程序，负责加载 `libsdk.so`/`sdk.dll` 并启动 FXEtherCAT 周期任务。
    *   作为 FXEtherCAT 服务的实际运行载体。

5.  **`SDK/FXEtherCATSDKWrapper/` (C# 封装库)**:
    *   一个 .NET 类库项目 (`FXEtherCATSDKWrapper.csproj`)。
    *   通过 P/Invoke 技术调用 `libsdk.so`/`sdk.dll` 导出的 C 函数。
    *   为上层应用提供简单易用的 C# 接口。

6.  **`Demo/Controller/` (C# 控制应用程序示例)**:
    *   一个 .NET 控制台应用程序项目 (`Controller.csproj`)。
    *   引用 `FXEtherCATSDKWrapper` 项目，演示如何使用 C# 库与 FXEtherCAT 系统交互。

## 构建与运行

### 先决条件

*   **C 编译环境**: GCC / Clang (Linux) 或 MSVC (Windows)。
*   **EtherCAT 主站协议栈**: 例如 IgH EtherCAT Master (Linux) 或其他兼容协议栈。
*   **.NET SDK**: .NET 6 或更高版本。
*   **Node.js**: 用于运行代码生成器。

### 构建步骤

#### 1. 生成 `sdk.c` 和 `sdk.h`

首先，需要使用代码生成器来创建 C 语言核心库。

```bash
# 1. 进入 SDK 目录
cd SDK

# 2. 安装 Node.js 依赖
npm install

# 3. 运行生成脚本
#    默认会使用 Demo/HX_16I16O/HX_16I16O.json 作为配置文件
#    你可以修改 simple-generate.js 来使用其他配置文件
node simple-generate.js

# 4. 返回项目根目录
cd ..
```

#### 2. 编译 C 语言库

当 `sdk.c` 和 `sdk.h` 生成后，将 C 语言部分编译成动态链接库 (`.so` 或 `.dll`) 和可执行文件。以下为 Linux 环境下的示例。

```bash
# 1. 编译核心库 sdk.c 为 libsdk.so
gcc -fPIC -shared -fvisibility=hidden sdk.c -o libsdk.so -lethercat -lpthread -lrt

# 2. 编译中间件宿主程序 middleware_host
gcc middleware_host.c -o middleware_host -L. -lsdk -lpthread
```

**注意**:
*   请确保 `sdk.h` 中导出的函数使用了正确的导出标记 (例如 `__attribute__((visibility("default")))` for GCC)。生成器已处理此问题。
*   在 Windows 上，你需要使用 MSVC 编译器，并使用 `__declspec(dllexport)` 导出函数。

#### 3. 编译 C# 项目

你可以使用 `dotnet` 命令行工具来编译 C# 项目。

```bash
# 1. 编译 C# 封装库
#    (这一步是可选的，因为构建 Controller 时会自动构建其依赖)
dotnet dotnet build SDK/FXEtherCATSDKWrapper/FXEtherCATSDKWrapper.csproj -c Release -r linux-arm64

# 2. 编译 C# 控制台应用
dotnet build Demo/Controller/Controller.csproj -c Release -r linux-arm64
dotnet build Demo/HX_16I16O/HX_16I16O.csproj -c Release -r linux-arm64
```

构建完成后，C# 可执行文件会出现在 `Demo/Controller/bin/Release/netX.X/` 或 `Demo/HX_16I16O/bin/Release/netX.X/` 目录下。

### 发布版本

如果需要将 C# 控制应用发布为独立的可执行文件（不依赖于目标机器上安装的 .NET 运行时），可以使用 `dotnet publish` 命令。

```bash
# 发布为特定平台的自包含应用 (以 Linux ARM64为例)
dotnet publish Demo/Controller/Controller.csproj -c Release -r linux-arm64 --self-contained true
dotnet publish Demo/HX_16I16O/HX_16I16O.csproj -c Release -r linux-arm64 --self-contained true
```

### 开发参考：创建新项目

如果您想从零开始创建类似的项目，可以使用以下 `dotnet` 命令：

```bash
# 生成一个新的控制台程序项目
dotnet new console -o MyConsoleApp

# 生成一个新的类库项目
dotnet new classlib -n MyLibrary
```

### 运行步骤

1.  **确保 EtherCAT 网络已连接并上电。**
2.  **确保 C 库 (`libsdk.so` 或 `sdk.dll`) 和 `middleware_host` 在可执行路径下。**
    *   在 Linux 上，可以将库文件所在的路径添加到 `LD_LIBRARY_PATH` 环境变量。
    *   在 Windows 上，可以将 DLL 文件和 `middleware_host.exe` 放置在与 C# 控制应用相同的目录。
    
    ```bash
    # Linux 示例: 将当前目录添加到库搜索路径
    export LD_LIBRARY_PATH=$LD_LIBRARY_PATH:$(pwd)
    ```

3.  **运行 `middleware_host` (后台运行)**:
    *   此程序需要在后台持续运行，以处理实时 FXEtherCAT 通信。
    *   在生产环境中，建议使用 `systemd` (Linux) 或 Windows 服务来管理其生命周期。

    ```bash
    # 运行中间件，可能需要 root 权限来访问硬件和设置实时优先级
    sudo ./middleware_host
    ```

4.  **运行 C# 控制应用程序**:
    ```bash
    # 运行编译好的 C# 应用
    dotnet run --project Demo/Controller/Controller.csproj
    ```
    C# 应用程序将通过 `FXEtherCATSDKWrapper` 与运行中的 `middleware_host` (通过共享内存)进行交互，实现对 FXEtherCAT 从站的控制。

## API 文档

C 语言核心库的 API 函数说明，请参考 `FXEtherCAT_SDK_API_Documentation.md`。

## 贡献

欢迎对本项目进行贡献！如果您有任何改进建议或发现任何问题，请随时创建 Issue 或提交 Pull Request。

## 许可证

本项目采用 [MIT 许可证](LICENSE)。 