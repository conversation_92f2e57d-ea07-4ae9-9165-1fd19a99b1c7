#define _GNU_SOURCE
#include "plc_io_lib.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <time.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <errno.h>
#include <sys/mman.h>
#include <sched.h>
#include <sys/resource.h>
#include <pthread.h>

// 信号灯闪烁状态
typedef struct {
    bool is_configured;
    signal_light_config_t config;
    bool is_blinking;
    signal_state_t red_state;
    signal_state_t green_state;
    signal_state_t yellow_state;
    signal_state_t buzzer_state;
    uint32_t blink_interval_ms;
    uint32_t blink_count;
    uint32_t current_blink_count;
    uint8_t current_red_output;
    uint8_t current_green_output;
    uint8_t current_yellow_output;
    uint8_t current_buzzer_output;
    pthread_t blink_thread;
    bool thread_running;
} signal_blink_state_t;

// 全局状态
static struct {
    bool initialized;
    bool connected;
    int socket_fd;
    plc_config_t config;
    plc_stats_t stats;
    pthread_mutex_t mutex;
    uint8_t* input_cache;
    uint8_t* output_cache;
    struct timespec last_read_time;
    signal_blink_state_t signal_state;

    // 事件驱动输入监控
    input_change_callback_t input_callback;
    pthread_t monitor_thread;
    volatile bool monitoring_active;
    uint8_t last_input_states[32];
} g_plc_state = {0};

// 内部函数声明
static plc_lib_result_t init_realtime_environment(void);
static plc_lib_result_t connect_to_device(void);
static plc_lib_result_t send_and_receive(const uint8_t* send_data, size_t send_len,
                                         uint8_t* recv_data, size_t recv_buf_size, size_t* recv_len);
static void update_stats(bool success, double latency_ms);
static double get_time_diff_ms(const struct timespec* start, const struct timespec* end);

// 信号灯内部函数声明
static void* signal_blink_thread(void* arg);
static plc_lib_result_t set_signal_output(int io_index, uint8_t value);

// 内部函数：不管理锁的写入所有输出函数
static plc_lib_result_t plc_write_all_outputs_internal(const uint8_t* output_data, int output_count);

plc_lib_result_t plc_init(const plc_config_t* config)
{
    if (!config) {
        return PLC_LIB_ERROR_INVALID_PARAM;
    }

    // 检查参数有效性
    if (strlen(config->ip_address) == 0 || config->port <= 0 || config->port > 65535) {
        return PLC_LIB_ERROR_INVALID_PARAM;
    }

    if (config->io_input_count < 0 || config->io_output_count < 0) {
        return PLC_LIB_ERROR_INVALID_PARAM;
    }

    // 初始化互斥锁
    if (pthread_mutex_init(&g_plc_state.mutex, NULL) != 0) {
        return PLC_LIB_ERROR_MEMORY;
    }

    pthread_mutex_lock(&g_plc_state.mutex);

    // 保存配置
    g_plc_state.config = *config;
    g_plc_state.socket_fd = -1;
    g_plc_state.connected = false;

    // 分配IO缓存
    if (config->io_input_count > 0) {
        g_plc_state.input_cache = calloc(config->io_input_count, sizeof(uint8_t));
        if (!g_plc_state.input_cache) {
            pthread_mutex_unlock(&g_plc_state.mutex);
            return PLC_LIB_ERROR_MEMORY;
        }
    }

    if (config->io_output_count > 0) {
        g_plc_state.output_cache = calloc(config->io_output_count, sizeof(uint8_t));
        if (!g_plc_state.output_cache) {
            free(g_plc_state.input_cache);
            pthread_mutex_unlock(&g_plc_state.mutex);
            return PLC_LIB_ERROR_MEMORY;
        }
    }

    // 初始化统计信息
    memset(&g_plc_state.stats, 0, sizeof(g_plc_state.stats));
    g_plc_state.stats.min_latency_ms = 999999.0;

    g_plc_state.initialized = true;

    pthread_mutex_unlock(&g_plc_state.mutex);

    // 初始化实时环境
    return init_realtime_environment();
}

plc_lib_result_t plc_connect(void)
{
    if (!g_plc_state.initialized) {
        return PLC_LIB_ERROR_NOT_INITIALIZED;
    }

    pthread_mutex_lock(&g_plc_state.mutex);

    if (g_plc_state.connected) {
        pthread_mutex_unlock(&g_plc_state.mutex);
        return PLC_LIB_OK; // 已经连接
    }

    plc_lib_result_t result = connect_to_device();
    if (result == PLC_LIB_OK) {
        g_plc_state.connected = true;
        g_plc_state.stats.is_connected = true;
    }

    pthread_mutex_unlock(&g_plc_state.mutex);
    return result;
}

plc_lib_result_t plc_disconnect(void)
{
    if (!g_plc_state.initialized) {
        return PLC_LIB_ERROR_NOT_INITIALIZED;
    }

    pthread_mutex_lock(&g_plc_state.mutex);

    if (g_plc_state.socket_fd >= 0) {
        close(g_plc_state.socket_fd);
        g_plc_state.socket_fd = -1;
    }

    g_plc_state.connected = false;
    g_plc_state.stats.is_connected = false;

    pthread_mutex_unlock(&g_plc_state.mutex);
    return PLC_LIB_OK;
}

plc_lib_result_t plc_read_all_io(uint8_t* input_data, int input_size,
                                uint8_t* output_data, int output_size,
                                int* actual_input_count, int* actual_output_count)
{
    if (!g_plc_state.initialized) {
        return PLC_LIB_ERROR_NOT_INITIALIZED;
    }

    if (!input_data || !output_data || !actual_input_count || !actual_output_count) {
        return PLC_LIB_ERROR_INVALID_PARAM;
    }

    pthread_mutex_lock(&g_plc_state.mutex);

    if (!g_plc_state.connected) {
        pthread_mutex_unlock(&g_plc_state.mutex);
        return PLC_LIB_ERROR_NETWORK;
    }

    struct timespec start_time, end_time;
    clock_gettime(CLOCK_MONOTONIC, &start_time);

    // 发送读取IO请求
    uint8_t request[] = {0xAA, 0x02, 0x55};
    uint8_t response[512];
    size_t response_len;

    plc_lib_result_t result = send_and_receive(request, sizeof(request), response, sizeof(response), &response_len);
    
    clock_gettime(CLOCK_MONOTONIC, &end_time);
    double latency = get_time_diff_ms(&start_time, &end_time);

    if (result == PLC_LIB_OK && response_len >= 5) {
        // 解析响应: AA [输入长度] [输入数据...] [输出长度] [输出数据...] 55
        if (response[0] == 0xAA && response[response_len-1] == 0x55) {
            int input_count = response[1];
            int output_count = response[2 + input_count];
            
            *actual_input_count = (input_count < input_size) ? input_count : input_size;
            *actual_output_count = (output_count < output_size) ? output_count : output_size;
            
            // 复制输入数据
            memcpy(input_data, &response[2], *actual_input_count);
            // 复制输出数据
            memcpy(output_data, &response[3 + input_count], *actual_output_count);
            
            // 更新缓存
            if (g_plc_state.input_cache && *actual_input_count <= g_plc_state.config.io_input_count) {
                memcpy(g_plc_state.input_cache, input_data, *actual_input_count);
            }
            if (g_plc_state.output_cache && *actual_output_count <= g_plc_state.config.io_output_count) {
                memcpy(g_plc_state.output_cache, output_data, *actual_output_count);
            }
            
            g_plc_state.last_read_time = end_time;
            update_stats(true, latency);
        } else {
            result = PLC_LIB_ERROR_NETWORK;
            update_stats(false, latency);
        }
    } else {
        update_stats(false, latency);
    }

    pthread_mutex_unlock(&g_plc_state.mutex);
    return result;
}

// [FIX] 内部函数：不管理锁的写入所有输出函数
// 假设调用者已经获取了锁
static plc_lib_result_t plc_write_all_outputs_internal(const uint8_t* output_data, int output_count)
{
    if (!g_plc_state.initialized) {
        return PLC_LIB_ERROR_NOT_INITIALIZED;
    }

    if (!output_data || output_count <= 0) {
        return PLC_LIB_ERROR_INVALID_PARAM;
    }

    // 锁已由外部函数处理，这里不再加锁
    if (!g_plc_state.connected) {
        return PLC_LIB_ERROR_NETWORK;
    }

    struct timespec start_time, end_time;
    clock_gettime(CLOCK_MONOTONIC, &start_time);

    // 构建写入请求: AA [长度] [数据...] 55
    uint8_t request[256];
    if (output_count + 3 > sizeof(request)) {
        return PLC_LIB_ERROR_INVALID_PARAM;
    }

    request[0] = 0xAA;
    request[1] = output_count;
    memcpy(&request[2], output_data, output_count);
    request[2 + output_count] = 0x55;

    uint8_t response[256];
    size_t response_len;
    size_t request_len = 3 + output_count;

    plc_lib_result_t result = send_and_receive(request, request_len, response, sizeof(response), &response_len);

    clock_gettime(CLOCK_MONOTONIC, &end_time);
    double latency = get_time_diff_ms(&start_time, &end_time);

    if (result == PLC_LIB_OK) {
        // 更新输出缓存
        if (g_plc_state.output_cache && output_count <= g_plc_state.config.io_output_count) {
            memcpy(g_plc_state.output_cache, output_data, output_count);
        }
        update_stats(true, latency);
    } else {
        update_stats(false, latency);
    }

    return result;
}

// [FIX] 公共函数：管理锁的写入所有输出函数
plc_lib_result_t plc_write_all_outputs(const uint8_t* output_data, int output_count)
{
    if (!g_plc_state.initialized) {
        return PLC_LIB_ERROR_NOT_INITIALIZED;
    }

    if (!output_data || output_count <= 0) {
        return PLC_LIB_ERROR_INVALID_PARAM;
    }

    pthread_mutex_lock(&g_plc_state.mutex);
    plc_lib_result_t result = plc_write_all_outputs_internal(output_data, output_count);
    pthread_mutex_unlock(&g_plc_state.mutex);

    return result;
}

plc_lib_result_t plc_read_input(int io_index, uint8_t* value)
{
    if (!g_plc_state.initialized) {
        return PLC_LIB_ERROR_NOT_INITIALIZED;
    }

    if (!value || io_index < 0 || io_index >= g_plc_state.config.io_input_count) {
        return PLC_LIB_ERROR_INVALID_PARAM;
    }

    pthread_mutex_lock(&g_plc_state.mutex);

    if (g_plc_state.input_cache) {
        *value = g_plc_state.input_cache[io_index];
        pthread_mutex_unlock(&g_plc_state.mutex);
        return PLC_LIB_OK;
    }

    pthread_mutex_unlock(&g_plc_state.mutex);
    return PLC_LIB_ERROR_NOT_INITIALIZED;
}

// [FIX] 修复死锁问题：使用内部版本的写入函数
plc_lib_result_t plc_write_output(int io_index, uint8_t value)
{
    if (!g_plc_state.initialized) {
        return PLC_LIB_ERROR_NOT_INITIALIZED;
    }

    if (io_index < 0 || io_index >= g_plc_state.config.io_output_count) {
        return PLC_LIB_ERROR_INVALID_PARAM;
    }

    pthread_mutex_lock(&g_plc_state.mutex);

    if (g_plc_state.output_cache) {
        g_plc_state.output_cache[io_index] = value ? 1 : 0;

        // [FIX] 使用内部版本避免死锁
        plc_lib_result_t result = plc_write_all_outputs_internal(g_plc_state.output_cache, g_plc_state.config.io_output_count);
        pthread_mutex_unlock(&g_plc_state.mutex);
        return result;
    }

    pthread_mutex_unlock(&g_plc_state.mutex);
    return PLC_LIB_ERROR_NOT_INITIALIZED;
}



plc_lib_result_t plc_get_stats(plc_stats_t* stats)
{
    if (!g_plc_state.initialized || !stats) {
        return PLC_LIB_ERROR_INVALID_PARAM;
    }

    pthread_mutex_lock(&g_plc_state.mutex);
    *stats = g_plc_state.stats;
    pthread_mutex_unlock(&g_plc_state.mutex);

    return PLC_LIB_OK;
}

plc_lib_result_t plc_reset_stats(void)
{
    if (!g_plc_state.initialized) {
        return PLC_LIB_ERROR_NOT_INITIALIZED;
    }

    pthread_mutex_lock(&g_plc_state.mutex);

    g_plc_state.stats.total_requests = 0;
    g_plc_state.stats.success_requests = 0;
    g_plc_state.stats.failed_requests = 0;
    g_plc_state.stats.timeout_count = 0;
    g_plc_state.stats.avg_latency_ms = 0.0;
    g_plc_state.stats.max_latency_ms = 0.0;
    g_plc_state.stats.min_latency_ms = 999999.0;

    pthread_mutex_unlock(&g_plc_state.mutex);
    return PLC_LIB_OK;
}

const char* plc_get_error_string(plc_lib_result_t error_code)
{
    switch (error_code) {
        case PLC_LIB_OK:
            return "Success";
        case PLC_LIB_ERROR_INVALID_PARAM:
            return "Invalid parameter";
        case PLC_LIB_ERROR_NOT_INITIALIZED:
            return "Library not initialized";
        case PLC_LIB_ERROR_NETWORK:
            return "Network error";
        case PLC_LIB_ERROR_TIMEOUT:
            return "Timeout error";
        case PLC_LIB_ERROR_REALTIME:
            return "Real-time error";
        case PLC_LIB_ERROR_MEMORY:
            return "Memory error";
        default:
            return "Unknown error";
    }
}

bool plc_is_connected(void)
{
    if (!g_plc_state.initialized) {
        return false;
    }

    pthread_mutex_lock(&g_plc_state.mutex);
    bool connected = g_plc_state.connected;
    pthread_mutex_unlock(&g_plc_state.mutex);

    return connected;
}

plc_lib_result_t plc_cleanup(void)
{
    if (!g_plc_state.initialized) {
        return PLC_LIB_ERROR_NOT_INITIALIZED;
    }

    plc_disconnect();

    // 停止输入监控
    plc_stop_input_monitoring();

    pthread_mutex_lock(&g_plc_state.mutex);

    // 释放缓存
    if (g_plc_state.input_cache) {
        free(g_plc_state.input_cache);
        g_plc_state.input_cache = NULL;
    }

    if (g_plc_state.output_cache) {
        free(g_plc_state.output_cache);
        g_plc_state.output_cache = NULL;
    }

    g_plc_state.initialized = false;

    pthread_mutex_unlock(&g_plc_state.mutex);

    // 清理实时环境
#ifdef __linux__
    munlockall();
    struct sched_param param = {0};
    sched_setscheduler(0, SCHED_OTHER, &param);
#endif

    pthread_mutex_destroy(&g_plc_state.mutex);
    memset(&g_plc_state, 0, sizeof(g_plc_state));

    return PLC_LIB_OK;
}

// 内部函数实现
static plc_lib_result_t init_realtime_environment(void)
{
    // 内存锁定
    if (g_plc_state.config.enable_memory_lock) {
        if (mlockall(MCL_CURRENT | MCL_FUTURE) == 0) {
            printf("✓ 内存页面已锁定\n");
        }
    }

#ifdef __linux__
    // 设置实时调度
    if (g_plc_state.config.rt_priority > 0 && g_plc_state.config.rt_priority <= 99) {
        struct sched_param param;
        param.sched_priority = g_plc_state.config.rt_priority;

        if (sched_setscheduler(0, SCHED_FIFO, &param) == 0) {
            g_plc_state.stats.current_rt_priority = g_plc_state.config.rt_priority;
            printf("✓ 实时调度已设置 (优先级: %d)\n", g_plc_state.config.rt_priority);
        }
    }

    // 设置CPU亲和性
    if (g_plc_state.config.cpu_core >= 0) {
        cpu_set_t cpuset;
        CPU_ZERO(&cpuset);
        CPU_SET(g_plc_state.config.cpu_core, &cpuset);

        if (sched_setaffinity(0, sizeof(cpuset), &cpuset) == 0) {
            g_plc_state.stats.current_cpu_core = g_plc_state.config.cpu_core;
            printf("✓ CPU亲和性已设置 (核心: %d)\n", g_plc_state.config.cpu_core);
        }
    }
#endif

    return PLC_LIB_OK;
}

static plc_lib_result_t connect_to_device(void)
{
    struct sockaddr_in server_addr;

    // 创建socket
    g_plc_state.socket_fd = socket(AF_INET, SOCK_STREAM, 0);
    if (g_plc_state.socket_fd < 0) {
        return PLC_LIB_ERROR_NETWORK;
    }

    // 设置服务器地址
    memset(&server_addr, 0, sizeof(server_addr));
    server_addr.sin_family = AF_INET;
    server_addr.sin_port = htons(g_plc_state.config.port);

    if (inet_pton(AF_INET, g_plc_state.config.ip_address, &server_addr.sin_addr) <= 0) {
        close(g_plc_state.socket_fd);
        g_plc_state.socket_fd = -1;
        return PLC_LIB_ERROR_NETWORK;
    }

    // 连接到服务器
    if (connect(g_plc_state.socket_fd, (struct sockaddr*)&server_addr, sizeof(server_addr)) < 0) {
        close(g_plc_state.socket_fd);
        g_plc_state.socket_fd = -1;
        return PLC_LIB_ERROR_NETWORK;
    }

    return PLC_LIB_OK;
}

static plc_lib_result_t send_and_receive(const uint8_t* send_data, size_t send_len,
                                        uint8_t* recv_data, size_t recv_buf_size, size_t* recv_len)
{
    // 发送数据
    ssize_t sent = send(g_plc_state.socket_fd, send_data, send_len, 0);
    if (sent != (ssize_t)send_len) {
        return PLC_LIB_ERROR_NETWORK;
    }

    // 设置接收超时
    struct timeval timeout;
    timeout.tv_sec = g_plc_state.config.timeout_ms / 1000;
    timeout.tv_usec = (g_plc_state.config.timeout_ms % 1000) * 1000;

    if (setsockopt(g_plc_state.socket_fd, SOL_SOCKET, SO_RCVTIMEO, &timeout, sizeof(timeout)) < 0) {
        return PLC_LIB_ERROR_NETWORK;
    }

    // 接收响应
    ssize_t received = recv(g_plc_state.socket_fd, recv_data, recv_buf_size, 0);
    if (received <= 0) {
        return PLC_LIB_ERROR_NETWORK;
    }
    *recv_len = received;
    return PLC_LIB_OK;
}

static void update_stats(bool success, double latency_ms)
{
    g_plc_state.stats.total_requests++;

    if (success) {
        g_plc_state.stats.success_requests++;

        if (latency_ms > g_plc_state.stats.max_latency_ms) {
            g_plc_state.stats.max_latency_ms = latency_ms;
        }

        if (latency_ms < g_plc_state.stats.min_latency_ms) {
            g_plc_state.stats.min_latency_ms = latency_ms;
        }

        // 更新平均延迟
        double total_success = g_plc_state.stats.success_requests;
        g_plc_state.stats.avg_latency_ms =
            (g_plc_state.stats.avg_latency_ms * (total_success - 1) + latency_ms) / total_success;
    } else {
        g_plc_state.stats.failed_requests++;
    }
}

static double get_time_diff_ms(const struct timespec* start, const struct timespec* end)
{
    return (end->tv_sec - start->tv_sec) * 1000.0 +
           (end->tv_nsec - start->tv_nsec) / 1000000.0;
}

plc_lib_result_t plc_verify_realtime_config(int* actual_cpu_core,
                                           int* actual_rt_priority,
                                           int* actual_policy,
                                           bool* memory_locked)
{
    if (!actual_cpu_core || !actual_rt_priority || !actual_policy || !memory_locked) {
        return PLC_LIB_ERROR_INVALID_PARAM;
    }

    *actual_cpu_core = -1;
    *actual_rt_priority = 0;
    *actual_policy = 0;
    *memory_locked = false;

#ifdef __linux__
    // 获取调度策略和优先级
    int policy = sched_getscheduler(0);
    if (policy >= 0) {
        *actual_policy = policy;
        struct sched_param param;
        if (sched_getparam(0, &param) == 0) {
            *actual_rt_priority = param.sched_priority;
        }
    }

    // 获取CPU亲和性
    cpu_set_t cpuset;
    CPU_ZERO(&cpuset);
    if (sched_getaffinity(0, sizeof(cpuset), &cpuset) == 0) {
        for (int i = 0; i < CPU_SETSIZE; i++) {
            if (CPU_ISSET(i, &cpuset)) {
                if (*actual_cpu_core == -1) {
                    *actual_cpu_core = i;
                }
            }
        }
    }

    // 检查内存锁定
    void* test_mem = malloc(4096);
    if (test_mem) {
        if (mlock(test_mem, 4096) == 0) {
            *memory_locked = true;
            munlock(test_mem, 4096);
        }
        free(test_mem);
    }
#endif

    return PLC_LIB_OK;
}

plc_lib_result_t plc_show_process_info(void)
{
    printf("\n=== PLC IO库进程实时状态 ===\n");

    pid_t pid = getpid();
    printf("进程ID: %d\n", pid);

#ifdef __linux__
    // 显示调度策略
    int policy = sched_getscheduler(0);
    const char* policy_name;
    switch (policy) {
        case SCHED_OTHER: policy_name = "SCHED_OTHER (普通调度)"; break;
        case SCHED_FIFO:  policy_name = "SCHED_FIFO (实时FIFO)"; break;
        case SCHED_RR:    policy_name = "SCHED_RR (实时轮转)"; break;
        default:          policy_name = "未知调度策略"; break;
    }
    printf("调度策略: %s\n", policy_name);

    // 显示实时优先级
    struct sched_param param;
    if (sched_getparam(0, &param) == 0) {
        printf("实时优先级: %d\n", param.sched_priority);
    }

    // 显示CPU亲和性
    cpu_set_t cpuset;
    CPU_ZERO(&cpuset);
    if (sched_getaffinity(0, sizeof(cpuset), &cpuset) == 0) {
        printf("CPU亲和性: ");
        bool first = true;
        for (int i = 0; i < CPU_SETSIZE; i++) {
            if (CPU_ISSET(i, &cpuset)) {
                if (!first) printf(", ");
                printf("%d", i);
                first = false;
            }
        }
        printf("\n");
    }

    // 显示Nice值
    int nice_val = getpriority(PRIO_PROCESS, 0);
    printf("Nice值: %d\n", nice_val);

    // 检查内存锁定
    void* test_mem = malloc(4096);
    bool memory_locked = false;
    if (test_mem) {
        if (mlock(test_mem, 4096) == 0) {
            memory_locked = true;
            munlock(test_mem, 4096);
        }
        free(test_mem);
    }
    printf("内存锁定: %s\n", memory_locked ? "已启用" : "未启用");

    if (g_plc_state.initialized) {
        printf("\n配置的参数:\n");
        printf("  目标CPU核心: %d\n", g_plc_state.config.cpu_core);
        printf("  目标实时优先级: %d\n", g_plc_state.config.rt_priority);
        printf("  内存锁定配置: %s\n", g_plc_state.config.enable_memory_lock ? "启用" : "禁用");
    }

    printf("\n验证命令:\n");
    printf("  ps -eLo pid,tid,class,rtprio,ni,pri,psr,pcpu,stat,comm | grep %d\n", pid);
    printf("  chrt -p %d\n", pid);
    printf("  taskset -cp %d\n", pid);
#else
    printf("详细信息仅在Linux系统上可用\n");
#endif

    printf("========================\n\n");
    return PLC_LIB_OK;
}

// ==================== 信号灯控制实现 ====================

plc_lib_result_t plc_config_signal_lights(const signal_light_config_t* config)
{
    if (!g_plc_state.initialized || !config) {
        return PLC_LIB_ERROR_INVALID_PARAM;
    }

    // 验证IO地址有效性
    if (config->red_light_io < 0 || config->red_light_io >= g_plc_state.config.io_output_count ||
        config->green_light_io < 0 || config->green_light_io >= g_plc_state.config.io_output_count ||
        config->yellow_light_io < 0 || config->yellow_light_io >= g_plc_state.config.io_output_count ||
        config->buzzer_io < 0 || config->buzzer_io >= g_plc_state.config.io_output_count) {
        return PLC_LIB_ERROR_INVALID_PARAM;
    }

    pthread_mutex_lock(&g_plc_state.mutex);

    // 如果正在闪烁，先停止
    if (g_plc_state.signal_state.is_blinking) {
        g_plc_state.signal_state.thread_running = false;
        pthread_mutex_unlock(&g_plc_state.mutex);

        if (g_plc_state.signal_state.blink_thread) {
            pthread_join(g_plc_state.signal_state.blink_thread, NULL);
        }

        pthread_mutex_lock(&g_plc_state.mutex);
        g_plc_state.signal_state.is_blinking = false;
    }

    // 保存配置
    g_plc_state.signal_state.config = *config;
    g_plc_state.signal_state.is_configured = true;

    // 初始化状态
    g_plc_state.signal_state.current_red_output = 0;
    g_plc_state.signal_state.current_green_output = 0;
    g_plc_state.signal_state.current_yellow_output = 0;
    g_plc_state.signal_state.current_buzzer_output = 0;

    pthread_mutex_unlock(&g_plc_state.mutex);

    printf("✓ 信号灯配置完成: 红灯IO%d, 绿灯IO%d, 黄灯IO%d, 蜂鸣器IO%d\n",
           config->red_light_io, config->green_light_io,
           config->yellow_light_io, config->buzzer_io);

    return PLC_LIB_OK;
}

plc_lib_result_t set_led_and_buzzer(signal_state_t red_state,
                                   signal_state_t green_state,
                                   signal_state_t yellow_state,
                                   signal_state_t buzzer_state,
                                   uint32_t blink_interval_ms,
                                   uint32_t blink_count)
{
    if (!g_plc_state.initialized || !g_plc_state.signal_state.is_configured) {
        return PLC_LIB_ERROR_NOT_INITIALIZED;
    }

    pthread_mutex_lock(&g_plc_state.mutex);

    // 如果已经在闪烁，先停止
    if (g_plc_state.signal_state.is_blinking) {
        g_plc_state.signal_state.thread_running = false;
        pthread_mutex_unlock(&g_plc_state.mutex);

        pthread_join(g_plc_state.signal_state.blink_thread, NULL);

        pthread_mutex_lock(&g_plc_state.mutex);
        g_plc_state.signal_state.is_blinking = false;
    }

    // 检查是否有任何信号需要闪烁
    bool need_blink = (red_state == SIGNAL_BLINK || green_state == SIGNAL_BLINK ||
                      yellow_state == SIGNAL_BLINK || buzzer_state == SIGNAL_BLINK);

    if (need_blink && blink_interval_ms > 0) {
        // 启动闪烁模式
        g_plc_state.signal_state.red_state = red_state;
        g_plc_state.signal_state.green_state = green_state;
        g_plc_state.signal_state.yellow_state = yellow_state;
        g_plc_state.signal_state.buzzer_state = buzzer_state;
        g_plc_state.signal_state.blink_interval_ms = blink_interval_ms;
        g_plc_state.signal_state.blink_count = blink_count;
        g_plc_state.signal_state.current_blink_count = 0;

        // 启动闪烁线程
        g_plc_state.signal_state.thread_running = true;
        g_plc_state.signal_state.is_blinking = true;

        int result = pthread_create(&g_plc_state.signal_state.blink_thread, NULL,
                                   signal_blink_thread, NULL);

        pthread_mutex_unlock(&g_plc_state.mutex);

        if (result != 0) {
            g_plc_state.signal_state.is_blinking = false;
            g_plc_state.signal_state.thread_running = false;
            return PLC_LIB_ERROR_REALTIME;
        }

        printf("✓ 信号灯闪烁已启动 (红:%d, 绿:%d, 黄:%d, 蜂鸣:%d, 间隔:%dms, 次数:%s)\n",
               red_state, green_state, yellow_state, buzzer_state, blink_interval_ms,
               blink_count == 0 ? "无限" : (char[]){blink_count/10+'0', blink_count%10+'0', '\0'});
    } else {
        // [OPTIMIZATION] 立即设置模式，只进行一次网络写入
        uint8_t red_output = (red_state == SIGNAL_ON) ? 1 : 0;
        uint8_t green_output = (green_state == SIGNAL_ON) ? 1 : 0;
        uint8_t yellow_output = (yellow_state == SIGNAL_ON) ? 1 : 0;
        uint8_t buzzer_output = (buzzer_state == SIGNAL_ON) ? 1 : 0;

        // 更新缓存中的值
        set_signal_output(g_plc_state.signal_state.config.red_light_io, red_output);
        set_signal_output(g_plc_state.signal_state.config.green_light_io, green_output);
        set_signal_output(g_plc_state.signal_state.config.yellow_light_io, yellow_output);
        set_signal_output(g_plc_state.signal_state.config.buzzer_io, buzzer_output);

        // [OPTIMIZATION] 一次性写入所有输出
        plc_lib_result_t result = plc_write_all_outputs_internal(g_plc_state.output_cache, g_plc_state.config.io_output_count);

        if (result == PLC_LIB_OK) {
             g_plc_state.signal_state.current_red_output = red_output;
             g_plc_state.signal_state.current_green_output = green_output;
             g_plc_state.signal_state.current_yellow_output = yellow_output;
             g_plc_state.signal_state.current_buzzer_output = buzzer_output;
             printf("✓ 信号灯状态已设置: 红%d 绿%d 黄%d 蜂鸣%d\n", red_output, green_output, yellow_output, buzzer_output);
        }

        pthread_mutex_unlock(&g_plc_state.mutex);
        return result;
    }

    return PLC_LIB_OK;
}





// ==================== 信号灯内部函数实现 ====================

static void* signal_blink_thread(void* arg)
{
    (void)arg; // 未使用的参数

    printf("✓ 信号灯闪烁线程已启动\n");

    bool blink_phase = false; // 闪烁相位：false=灭, true=亮
    struct timespec sleep_time;
    uint32_t interval_ms;

    while (g_plc_state.signal_state.thread_running) {
        pthread_mutex_lock(&g_plc_state.mutex);

        if (!g_plc_state.signal_state.thread_running) {
            pthread_mutex_unlock(&g_plc_state.mutex);
            break;
        }

        interval_ms = g_plc_state.signal_state.blink_interval_ms;

        // 计算每个信号的当前输出状态
        uint8_t red_output = 0, green_output = 0, yellow_output = 0, buzzer_output = 0;

        // 红灯输出计算
        switch (g_plc_state.signal_state.red_state) {
            case SIGNAL_OFF: red_output = 0; break;
            case SIGNAL_ON: red_output = 1; break;
            case SIGNAL_BLINK: red_output = blink_phase ? 1 : 0; break;
        }

        // 绿灯输出计算
        switch (g_plc_state.signal_state.green_state) {
            case SIGNAL_OFF: green_output = 0; break;
            case SIGNAL_ON: green_output = 1; break;
            case SIGNAL_BLINK: green_output = blink_phase ? 1 : 0; break;
        }

        // 黄灯输出计算
        switch (g_plc_state.signal_state.yellow_state) {
            case SIGNAL_OFF: yellow_output = 0; break;
            case SIGNAL_ON: yellow_output = 1; break;
            case SIGNAL_BLINK: yellow_output = blink_phase ? 1 : 0; break;
        }

        // 蜂鸣器输出计算
        switch (g_plc_state.signal_state.buzzer_state) {
            case SIGNAL_OFF: buzzer_output = 0; break;
            case SIGNAL_ON: buzzer_output = 1; break;
            case SIGNAL_BLINK: buzzer_output = blink_phase ? 1 : 0; break;
        }

        // [OPTIMIZATION] 更新所有信号在缓存中的状态
        set_signal_output(g_plc_state.signal_state.config.red_light_io, red_output);
        set_signal_output(g_plc_state.signal_state.config.green_light_io, green_output);
        set_signal_output(g_plc_state.signal_state.config.yellow_light_io, yellow_output);
        set_signal_output(g_plc_state.signal_state.config.buzzer_io, buzzer_output);

        // [OPTIMIZATION] 一次性将缓存写入PLC
        plc_write_all_outputs_internal(g_plc_state.output_cache, g_plc_state.config.io_output_count);

        // 更新当前输出状态
        g_plc_state.signal_state.current_red_output = red_output;
        g_plc_state.signal_state.current_green_output = green_output;
        g_plc_state.signal_state.current_yellow_output = yellow_output;
        g_plc_state.signal_state.current_buzzer_output = buzzer_output;

        pthread_mutex_unlock(&g_plc_state.mutex);

        // 使用用户指定的间隔时间
        sleep_time.tv_sec = interval_ms / 1000;
        sleep_time.tv_nsec = (interval_ms % 1000) * 1000000;
        nanosleep(&sleep_time, NULL);

        // 切换闪烁相位
        blink_phase = !blink_phase;

        // 检查闪烁次数限制
        if (g_plc_state.signal_state.blink_count > 0) {
            g_plc_state.signal_state.current_blink_count++;
            if (g_plc_state.signal_state.current_blink_count >= g_plc_state.signal_state.blink_count * 2) {
                // 达到指定次数，停止闪烁
                printf("✓ 信号灯闪烁完成 (%d次)\n", g_plc_state.signal_state.blink_count);
                g_plc_state.signal_state.thread_running = false;
                g_plc_state.signal_state.is_blinking = false;
                break;
            }
        }
    }

    printf("✓ 信号灯闪烁线程已退出\n");
    return NULL;
}



// [OPTIMIZATION] 修改此函数，使其只更新缓存，不立即写入
// 写入操作将由一个统一的函数完成
static plc_lib_result_t set_signal_output(int io_index, uint8_t value)
{
    if (io_index < 0 || io_index >= g_plc_state.config.io_output_count) {
        return PLC_LIB_ERROR_INVALID_PARAM;
    }

    // 假设调用者已经加锁
    if (g_plc_state.output_cache) {
        g_plc_state.output_cache[io_index] = value ? 1 : 0;
        return PLC_LIB_OK;
    }

    return PLC_LIB_ERROR_NOT_INITIALIZED;
}

// ==================== 事件驱动输入监控实现 ====================

/**
 * 输入监控线程函数
 */
static void* input_monitor_thread(void* arg) {
    (void)arg; // 避免未使用参数警告

    // 设置实时优先级
    struct sched_param param;
    param.sched_priority = 90; // 高优先级，但低于主线程
    if (pthread_setschedparam(pthread_self(), SCHED_FIFO, &param) != 0) {
        printf("警告: 无法设置监控线程实时优先级 (需要root权限)\n");
    }

    uint8_t current_states[32];
    struct timespec ts;

    printf("输入监控线程已启动 (优先级: %d)\n", param.sched_priority);

    while (g_plc_state.monitoring_active) {
        // 读取当前输入状态
        int actual_input_count = 0;
        int actual_output_count = 0;
        uint8_t dummy_output[32];

        plc_lib_result_t result = plc_read_all_io(current_states, sizeof(current_states),
                                                  dummy_output, sizeof(dummy_output),
                                                  &actual_input_count, &actual_output_count);

        if (result == PLC_LIB_OK && g_plc_state.input_callback) {
            // 获取高精度时间戳
            clock_gettime(CLOCK_MONOTONIC, &ts);
            uint64_t timestamp_us = ts.tv_sec * 1000000ULL + ts.tv_nsec / 1000;

            // 检测状态变化
            int max_inputs = (actual_input_count > 32) ? 32 : actual_input_count;
            for (int i = 0; i < max_inputs; i++) {
                if (current_states[i] != g_plc_state.last_input_states[i]) {
                    // 状态发生变化，立即回调
                    g_plc_state.input_callback(i, current_states[i], timestamp_us);
                    g_plc_state.last_input_states[i] = current_states[i];
                }
            }
        }

        // 1ms轮询间隔，比C#的50ms快50倍
        usleep(1000);
    }

    printf("输入监控线程已停止\n");
    return NULL;
}

/**
 * 注册输入变化回调函数
 */
plc_lib_result_t plc_register_input_callback(input_change_callback_t callback) {
    if (!g_plc_state.initialized) {
        return PLC_LIB_ERROR_NOT_INITIALIZED;
    }

    if (callback == NULL) {
        return PLC_LIB_ERROR_INVALID_PARAM;
    }

    pthread_mutex_lock(&g_plc_state.mutex);
    g_plc_state.input_callback = callback;
    pthread_mutex_unlock(&g_plc_state.mutex);

    printf("输入变化回调函数已注册\n");
    return PLC_LIB_OK;
}

/**
 * 注销输入变化回调函数
 */
plc_lib_result_t plc_unregister_input_callback(void) {
    if (!g_plc_state.initialized) {
        return PLC_LIB_ERROR_NOT_INITIALIZED;
    }

    pthread_mutex_lock(&g_plc_state.mutex);
    g_plc_state.input_callback = NULL;
    pthread_mutex_unlock(&g_plc_state.mutex);

    printf("输入变化回调函数已注销\n");
    return PLC_LIB_OK;
}

/**
 * 启动输入监控
 */
plc_lib_result_t plc_start_input_monitoring(void) {
    if (!g_plc_state.initialized) {
        return PLC_LIB_ERROR_NOT_INITIALIZED;
    }

    if (!g_plc_state.connected) {
        return PLC_LIB_ERROR_NETWORK;
    }

    if (g_plc_state.input_callback == NULL) {
        return PLC_LIB_ERROR_INVALID_PARAM;
    }

    if (g_plc_state.monitoring_active) {
        printf("输入监控已经在运行中\n");
        return PLC_LIB_OK;
    }

    // 初始化状态
    memset(g_plc_state.last_input_states, 0, sizeof(g_plc_state.last_input_states));
    g_plc_state.monitoring_active = true;

    // 创建监控线程
    if (pthread_create(&g_plc_state.monitor_thread, NULL, input_monitor_thread, NULL) != 0) {
        g_plc_state.monitoring_active = false;
        return PLC_LIB_ERROR_REALTIME;
    }

    printf("输入监控已启动\n");
    return PLC_LIB_OK;
}

/**
 * 停止输入监控
 */
plc_lib_result_t plc_stop_input_monitoring(void) {
    if (!g_plc_state.initialized) {
        return PLC_LIB_ERROR_NOT_INITIALIZED;
    }

    if (!g_plc_state.monitoring_active) {
        printf("输入监控未在运行\n");
        return PLC_LIB_OK;
    }

    // 停止监控线程
    g_plc_state.monitoring_active = false;

    // 等待线程结束
    if (pthread_join(g_plc_state.monitor_thread, NULL) != 0) {
        printf("警告: 等待监控线程结束时出错\n");
    }

    printf("输入监控已停止\n");
    return PLC_LIB_OK;
}
