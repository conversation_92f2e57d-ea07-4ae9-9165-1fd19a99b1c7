{"compilerOptions": {"target": "es2017", "module": "commonjs", "lib": ["es2017", "es2017.promise"], "outDir": "./dist", "rootDir": "./", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true, "typeRoots": ["./SDK/node_modules/@types", "./node_modules/@types"], "types": ["node"]}, "include": ["*.ts", "**/*.ts"], "exclude": ["node_modules", "SDK/node_modules", "dist"]}