using System;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using System.Diagnostics;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Linq;
using FXEtherCAT.SDK; // 添加SDK引用

namespace FXEtherCATControl
{
    public class FXEtherCATController : IDisposable
    {
        private readonly FXEtherCATSDK _sdk; // 使用SDK替代共享内存

        // 定义常量，对应于HX_16I16O模块的PDO映射
        private const ushort SLAVE_NUMBER = 0;
        
        // 输出PDO
        private const ushort OUTPUT_BYTE0_INDEX = 0x7100;
        private const byte OUTPUT_BYTE0_SUBINDEX = 1;
        private const ushort OUTPUT_BYTE1_INDEX = 0x7100;
        private const byte OUTPUT_BYTE1_SUBINDEX = 2;
        
        // 输入PDO
        private const ushort INPUT_BYTE0_INDEX = 0x6000;
        private const byte INPUT_BYTE0_SUBINDEX = 1;
        private const ushort INPUT_BYTE1_INDEX = 0x6000;
        private const byte INPUT_BYTE1_SUBINDEX = 2;
        
        // 状态PDO
        private const ushort MODULE_STATE_INDEX = 0x8002;
        private const byte MODULE_STATE_SUBINDEX = 0;
        private const ushort MODULE_ERR_NUM_INDEX = 0x8003;
        private const byte MODULE_ERR_NUM_SUBINDEX = 0;
        private const ushort MODULE_STATE2_INDEX = 0x8102;
        private const byte MODULE_STATE2_SUBINDEX = 0;
        private const ushort MODULE_ERR_NUM2_INDEX = 0x8103;
        private const byte MODULE_ERR_NUM2_SUBINDEX = 0;

        // Linux RT 相关定义
        private enum SchedPolicy
        {
            SCHED_FIFO = 1
        }

        [System.Runtime.InteropServices.StructLayout(System.Runtime.InteropServices.LayoutKind.Sequential)]
        private struct SchedParam
        {
            public int sched_priority;
        }

        [System.Runtime.InteropServices.DllImport("libc", EntryPoint = "sched_setscheduler")]
        private static extern int sched_setscheduler(int pid, int policy, ref SchedParam param);

        [System.Runtime.InteropServices.DllImport("libc", EntryPoint = "sched_get_priority_max")]
        private static extern int sched_get_priority_max(int policy);

        public FXEtherCATController()
        {
            _sdk = new FXEtherCATSDK();
        }

        public bool WaitForIOReady(CancellationToken cancellationToken = default)
        {
            Console.WriteLine("等待远程IO就绪...");
            int timeoutMs = 5000; // 5秒超时
            int elapsed = 0;
            
            while (!cancellationToken.IsCancellationRequested && elapsed < timeoutMs)
            {
                // 获取从站状态
                if (_sdk.GetSlaveDeviceStatus(SLAVE_NUMBER, out SlaveDeviceStatus status))
                {
                    // 检查在线状态和运行状态
                    if (status.IsOnline && status.IsOperational)
                    {
                        Console.WriteLine("远程IO已就绪");
                        return true;
                    }
                }
                
                Thread.Sleep(100);
                elapsed += 100;
            }
            
            if (elapsed >= timeoutMs)
            {
                Console.WriteLine("等待远程IO就绪超时");
                if (_sdk.GetSlaveDeviceStatus(SLAVE_NUMBER, out SlaveDeviceStatus status))
                {
                    Console.WriteLine($"在线状态: {status.IsOnline}");
                    Console.WriteLine($"运行状态: {status.IsOperational}");
                    Console.WriteLine($"AL状态: {status.AlState}");
                }
            }
            
            return false;
        }

        // 将字节值可视化为实心圆(●)和空心圆(○)
        private string VisualizeBytePattern(byte value)
        {
            StringBuilder sb = new StringBuilder();
            for (int i = 7; i >= 0; i--)
            {
                bool isOn = (value & (1 << i)) != 0;
                sb.Append(isOn ? "●" : "○");
                if (i == 4) sb.Append(" "); // 每4位添加一个空格，增强可读性
            }
            return sb.ToString();
        }

        public void SetDigitalOutput(byte firstGroupOutputs, byte secondGroupOutputs, string description = "设置数字输出")
        {
            // 使用SDK写入输出数据
            _sdk.WriteSlave(SLAVE_NUMBER, OUTPUT_BYTE1_INDEX, OUTPUT_BYTE1_SUBINDEX, firstGroupOutputs);
            _sdk.WriteSlave(SLAVE_NUMBER, OUTPUT_BYTE0_INDEX, OUTPUT_BYTE0_SUBINDEX, secondGroupOutputs);
            
            // 计算完整模式值
            int pattern = (firstGroupOutputs << 8) | secondGroupOutputs;
            
            // 显示输出状态
            Console.WriteLine($"设置数字输出: 0x{pattern:X4} (第一组:0x{firstGroupOutputs:X2}, 第二组:0x{secondGroupOutputs:X2})");
            Console.Write("状态位: ");
            for (int i = 15; i >= 0; i--)  // 从高位到低位显示，更符合阅读习惯
            {
                bool isOn = (pattern & (1 << i)) != 0;
                Console.Write(isOn ? "1" : "0");
                if (i == 8)
                    Console.Write(" ");
            }
            Console.WriteLine();
            
            // 可视化显示
            Console.WriteLine($"可视化状态: 第一组: {VisualizeBytePattern(firstGroupOutputs)} | 第二组: {VisualizeBytePattern(secondGroupOutputs)}");
            
            if (description != null)
            {
                Console.WriteLine($"描述: {description}");
            }
        }

        public (int inputPattern, byte firstGroupInputs, byte secondGroupInputs, int[] moduleStates, int[] errorNums) GetDigitalInputsAndStatus()
        {
            // 使用SDK读取输入数据
            _sdk.ReadSlave(SLAVE_NUMBER, INPUT_BYTE0_INDEX, INPUT_BYTE0_SUBINDEX, out int secondGroupInputsValue);
            _sdk.ReadSlave(SLAVE_NUMBER, INPUT_BYTE1_INDEX, INPUT_BYTE1_SUBINDEX, out int firstGroupInputsValue);

            byte secondGroupInputs = (byte)secondGroupInputsValue;
            byte firstGroupInputs = (byte)firstGroupInputsValue;

            // 合并为整数
            int inputPattern = (firstGroupInputs << 8) | secondGroupInputs;

            // 读取模块状态
            _sdk.ReadSlave(SLAVE_NUMBER, MODULE_STATE_INDEX, MODULE_STATE_SUBINDEX, out int moduleState1);
            _sdk.ReadSlave(SLAVE_NUMBER, MODULE_ERR_NUM_INDEX, MODULE_ERR_NUM_SUBINDEX, out int errorNum1);
            _sdk.ReadSlave(SLAVE_NUMBER, MODULE_STATE2_INDEX, MODULE_STATE2_SUBINDEX, out int moduleState2);
            _sdk.ReadSlave(SLAVE_NUMBER, MODULE_ERR_NUM2_INDEX, MODULE_ERR_NUM2_SUBINDEX, out int errorNum2);
            
            int[] moduleStates = new int[2] { moduleState1, moduleState2 };
            int[] errorNums = new int[2] { errorNum1, errorNum2 };
            
            // 显示输入状态
            Console.WriteLine($"读取数字输入: 0x{inputPattern:X4} (第一组:0x{firstGroupInputs:X2}, 第二组:0x{secondGroupInputs:X2})");
            Console.Write("输入状态位: ");
            for (int i = 15; i >= 0; i--)
            {
                bool isOn = (inputPattern & (1 << i)) != 0;
                Console.Write(isOn ? "1" : "0");
                if (i == 8)
                    Console.Write(" ");
            }
            Console.WriteLine();
            
            // 可视化显示
            Console.WriteLine($"可视化状态: 第一组: {VisualizeBytePattern(firstGroupInputs)} | 第二组: {VisualizeBytePattern(secondGroupInputs)}");
            
            return (inputPattern, firstGroupInputs, secondGroupInputs, moduleStates, errorNums);
        }

        public void EnterRealtimeMode()
        {
            var param = new SchedParam { sched_priority = sched_get_priority_max((int)SchedPolicy.SCHED_FIFO) };
            if (sched_setscheduler(0, (int)SchedPolicy.SCHED_FIFO, ref param) != 0)
            {
                Console.WriteLine("Warning: Failed to set SCHED_FIFO scheduler");
            }
        }

        public async Task RunBasicTest(CancellationToken cancellationToken)
        {
            try
            {
                if (!WaitForIOReady(cancellationToken))
                {
                    Console.WriteLine("IO未就绪，退出控制序列");
                    return;
                }

                Console.WriteLine("开始IO控制和监听...");

                // 设置默认输出状态
                SetDigitalOutput(0xDD, 0xDD, "默认输出状态 (0xDD = 11011101)");
                
                // 读取初始输入状态
                var (lastInputPattern, lastFirstGroupInputs, lastSecondGroupInputs, _, _) = GetDigitalInputsAndStatus();
                Console.WriteLine("开始监听输入变化...");

                // 记录上次打印状态的时间
                var lastStatusPrintTime = DateTime.Now;
                const int STATUS_PRINT_INTERVAL_MS = 1000; // 每秒打印一次状态
                
                // 无限循环监听输入
                while (!cancellationToken.IsCancellationRequested)
                {
                    // 短暂等待，避免CPU占用过高
                    await Task.Delay(50, cancellationToken);
                    
                    // 每秒打印一次从站状态
                    var now = DateTime.Now;
                    if ((now - lastStatusPrintTime).TotalMilliseconds >= STATUS_PRINT_INTERVAL_MS)
                    {
                        Console.WriteLine("\n从站状态:");
                        if (_sdk.GetSlaveDeviceStatus(SLAVE_NUMBER, out SlaveDeviceStatus status))
                        {
                            Console.WriteLine($"在线状态: {(status.IsOnline ? "在线" : "离线")} ({(status.IsOnline ? 1 : 0)})");
                            Console.WriteLine($"运行状态: {(status.IsOperational ? "运行中" : "未运行")} ({(status.IsOperational ? 1 : 0)})");
                            Console.WriteLine($"AL状态: {status.AlState}");
                        }
                        Console.WriteLine("------------------------");
                        
                        lastStatusPrintTime = now;
                    }
                    
                    // 读取当前输入
                    _sdk.ReadSlave(SLAVE_NUMBER, INPUT_BYTE0_INDEX, INPUT_BYTE0_SUBINDEX, out int secondGroupInputsValue);
                    _sdk.ReadSlave(SLAVE_NUMBER, INPUT_BYTE1_INDEX, INPUT_BYTE1_SUBINDEX, out int firstGroupInputsValue);
                    
                    byte secondGroupInputs = (byte)secondGroupInputsValue;
                    byte firstGroupInputs = (byte)firstGroupInputsValue;
                    int currentInputPattern = (firstGroupInputs << 8) | secondGroupInputs;
                    
                    // 检查输入是否有变化
                    if (currentInputPattern != lastInputPattern)
                    {
                        Console.WriteLine("\n检测到输入变化!");
                        Console.WriteLine($"输入状态变化: 0x{lastInputPattern:X4} => 0x{currentInputPattern:X4}");
                        
                        // 显示哪些位发生了变化
                        Console.Write("变化的位: ");
                        int changedBits = lastInputPattern ^ currentInputPattern;
                        for (int i = 15; i >= 0; i--)
                        {
                            bool isChanged = (changedBits & (1 << i)) != 0;
                            Console.Write(isChanged ? "1" : "0");
                            if (i == 8)
                                Console.Write(" ");
                        }
                        Console.WriteLine();
                        
                        // 显示可视化的输入状态
                        Console.WriteLine($"当前可视化状态: 第一组: {VisualizeBytePattern(firstGroupInputs)} | 第二组: {VisualizeBytePattern(secondGroupInputs)}");
                        
                        // 更新上次状态
                        lastInputPattern = currentInputPattern;
                        lastFirstGroupInputs = firstGroupInputs;
                        lastSecondGroupInputs = secondGroupInputs;
                        
                        // 读取模块状态
                        _sdk.ReadSlave(SLAVE_NUMBER, MODULE_STATE_INDEX, MODULE_STATE_SUBINDEX, out int moduleState1);
                        _sdk.ReadSlave(SLAVE_NUMBER, MODULE_ERR_NUM_INDEX, MODULE_ERR_NUM_SUBINDEX, out int errorNum1);
                        _sdk.ReadSlave(SLAVE_NUMBER, MODULE_STATE2_INDEX, MODULE_STATE2_SUBINDEX, out int moduleState2);
                        _sdk.ReadSlave(SLAVE_NUMBER, MODULE_ERR_NUM2_INDEX, MODULE_ERR_NUM2_SUBINDEX, out int errorNum2);
                        
                        int[] moduleStates = new int[2] { moduleState1, moduleState2 };
                        int[] errorNums = new int[2] { errorNum1, errorNum2 };
                        
                        // 检查模块状态
                        for (int i = 0; i < moduleStates.Length; i++)
                        {
                            if (moduleStates[i] != 0)
                            {
                                Console.WriteLine($"模块 {i} 错误: State=0x{moduleStates[i]:X4}, ErrorNum={errorNums[i]}");
                            }
                        }
                    }
                }
            }
            catch (OperationCanceledException)
            {
                Console.WriteLine("控制序列被取消");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"控制序列错误: {ex.Message}");
            }
            finally
            {
                // 清除所有输出
                SetDigitalOutput(0, 0, "清除所有输出");
                Console.WriteLine("控制序列结束");
            }
        }

        public void Dispose()
        {
            _sdk?.Dispose();
        }
    }

    class Program
    {
        // 主站信息响应模型
        private class MasterInfo
        {
            public int masterIndex { get; set; }
        }

        private class MasterInfoResponse
        {
            public int code { get; set; }
            public string msg { get; set; }
            public MasterInfo data { get; set; }
        }

        // 从站状态响应模型
        private class SlaveStatus
        {
            public int index { get; set; }
            public int master { get; set; }
            public string name { get; set; }
            public string state { get; set; }
            public string group { get; set; }
            public bool online { get; set; }
            public int operationalStatus { get; set; }
            public int alState { get; set; }
            public string vendorId { get; set; }
            public string productCode { get; set; }
            public string position { get; set; }
        }

        private class SlaveStatusResponse
        {
            public int totalCount { get; set; }
            public int opCount { get; set; }
            public int preopCount { get; set; }
            public int otherCount { get; set; }
            public List<SlaveStatus> slaves { get; set; }
        }

        private class SlaveStatusApiResponse
        {
            public int code { get; set; }
            public string msg { get; set; }
            public SlaveStatusResponse data { get; set; }
        }

        private static bool IsValidHexValue(string hex)
        {
            if (string.IsNullOrEmpty(hex)) return false;
            // 移除0x前缀后检查是否全为0
            string value = hex.StartsWith("0x") ? hex.Substring(2) : hex;
            return !value.All(c => c == '0');
        }

        // 获取主站信息
        private static async Task<int> GetMasterIndex(HttpClient client, string programName)
        {
            try
            {
                Console.WriteLine("正在获取主站信息...");
                var response = await client.GetAsync(
                    $"http://127.0.0.1:3000/api/programs/master-info?programName={programName}"
                );

                if (!response.IsSuccessStatusCode)
                {
                    Console.WriteLine($"获取主站信息失败: {response.StatusCode}");
                    return -1;
                }

                var responseContent = await response.Content.ReadAsStringAsync();
                Console.WriteLine($"API返回数据: {responseContent}"); // 添加调试输出
                var masterResponse = JsonSerializer.Deserialize<MasterInfoResponse>(responseContent);

                if (masterResponse.code != 0)
                {
                    Console.WriteLine($"获取主站信息API错误: {masterResponse.msg}");
                    return -1;
                }

                Console.WriteLine($"获取到主站索引: {masterResponse.data.masterIndex}");
                return masterResponse.data.masterIndex;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取主站信息时发生错误: {ex.Message}");
                Console.WriteLine($"错误详情: {ex}"); // 添加更详细的错误信息
                return -1;
            }
        }

        private static async Task<bool> CheckSlavesStatus(HttpClient client, int masterIndex)
        {
            const int MAX_RETRIES = 10;
            const int RETRY_DELAY_MS = 1000;

            for (int i = 0; i < MAX_RETRIES; i++)
            {
                try
                {
                    Console.WriteLine($"正在检查从站状态，第{i + 1}次尝试...");
                    
                    var response = await client.GetAsync(
                        "http://127.0.0.1:3000/api/ethercat/all-slaves-status?detail=1"
                    );

                    if (!response.IsSuccessStatusCode)
                    {
                        Console.WriteLine($"获取从站状态失败: {response.StatusCode}");
                        continue;
                    }

                    var responseContent = await response.Content.ReadAsStringAsync();
                    var statusResponse = JsonSerializer.Deserialize<SlaveStatusApiResponse>(responseContent);

                    if (statusResponse.code != 0)
                    {
                        Console.WriteLine($"API错误: {statusResponse.msg}");
                        continue;
                    }

                    var data = statusResponse.data;
                    
                    // 筛选出同主站下的从站
                    var masterSlaves = data.slaves.Where(s => s.master == masterIndex).ToList();
                    if (!masterSlaves.Any())
                    {
                        Console.WriteLine($"未找到主站 {masterIndex} 下的从站");
                        await Task.Delay(RETRY_DELAY_MS);
                        continue;
                    }

                    Console.WriteLine($"\n当前主站 {masterIndex} 下的从站状态:");
                    foreach (var slave in masterSlaves)
                    {
                        Console.WriteLine($"从站索引: {slave.index}");
                        Console.WriteLine($"名称: {slave.name}");
                        Console.WriteLine($"状态: {slave.state}");
                        Console.WriteLine($"分组: {slave.group}");
                        Console.WriteLine($"在线: {slave.online}");
                        Console.WriteLine($"运行状态: {slave.operationalStatus}");
                        Console.WriteLine($"AL状态: {slave.alState}");
                        Console.WriteLine($"位置: {slave.position}");
                        Console.WriteLine("------------------------");
                    }

                    // 检查所有从站是否都在线且状态正常
                    bool allSlavesOk = masterSlaves.All(s => 
                        s.online && 
                        (s.state == "OP" || s.state == "PREOP") && 
                        IsValidHexValue(s.vendorId) && 
                        IsValidHexValue(s.productCode)
                    );

                    if (allSlavesOk)
                    {
                        Console.WriteLine("所有从站状态正常");
                        return true;
                    }

                    Console.WriteLine("部分从站状态异常，等待重试...");
                    await Task.Delay(RETRY_DELAY_MS);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"检查从站状态时发生错误: {ex.Message}");
                    await Task.Delay(RETRY_DELAY_MS);
                }
            }

            Console.WriteLine($"从站状态检查失败，已达到最大重试次数({MAX_RETRIES})");
            return false;
        }

        static async Task Main(string[] args)
        {
            try 
            {
                // 创建HTTP客户端
                using var client = new HttpClient();
                client.DefaultRequestHeaders.Accept.Add(
                    new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json")
                );
                
                string programName = Path.GetFileName(Process.GetCurrentProcess().MainModule?.FileName ?? "unknown");
                
                // 获取主站索引
                int masterIndex = await GetMasterIndex(client, programName);
                if (masterIndex == -1)
                {
                    Console.WriteLine("无法获取主站索引，程序退出");
                    return;
                }
                
                // 检查从站状态（使用主站索引）
                Console.WriteLine("开始检查从站状态...");
                if (!await CheckSlavesStatus(client, masterIndex))
                {
                    Console.WriteLine("从站状态检查失败，程序退出");
                    return;
                }
                
                await StartMiddleware(programName, true);

                Console.WriteLine($"启动FXEtherCAT控制程序...");

                using var controller = new FXEtherCATController();
                controller.EnterRealtimeMode();

                var cts = new CancellationTokenSource();

                // 添加SIGTERM信号处理
                AppDomain.CurrentDomain.ProcessExit += (s, e) => {
                    Console.WriteLine("收到SIGTERM信号，正在优雅退出...");
                    cts.Cancel();
                };

                Console.CancelKeyPress += (s, e) => {
                    e.Cancel = true;
                    cts.Cancel();
                };

                try
                {
                    await controller.RunBasicTest(cts.Token);
                }
                catch (OperationCanceledException)
                {
                    Console.WriteLine("程序正在停止...");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"执行错误: {ex.Message}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"程序启动错误: {ex.Message}");
            }
        }
        private static async Task<string> StartMiddleware(string programName, bool sdk)
        {
            try
            {
                using var client = new HttpClient();
                client.DefaultRequestHeaders.Accept.Add(
                    new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json")
                );

                var requestData = new { programName, sdk };
                var jsonContent = JsonSerializer.Serialize(requestData);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                Console.WriteLine($"正在请求EtherCAT中间层服务 ({programName})...");
                var response = await client.PostAsync(
                    "http://127.0.0.1:3000/api/programs/start-middleware",
                    content
                );

                if (!response.IsSuccessStatusCode)
                {
                    Console.WriteLine($"请求中间件失败: {response.StatusCode}");
                    return null;
                }

                var responseContent = await response.Content.ReadAsStringAsync();
                Console.WriteLine($"中间件返回数据: {responseContent}");

                // 解析JSON响应
                var responseData = JsonSerializer.Deserialize<MiddlewareResponse>(responseContent);
                if (responseData.code != 0)
                {
                    string errorMsg = !string.IsNullOrEmpty(responseData.msg) ? responseData.msg : "未知错误";
                    Console.WriteLine($"错误：中间件返回错误码 {responseData.code}，错误信息：{errorMsg}");
                    return null;
                }

                if (responseData.data == null || string.IsNullOrEmpty(responseData.data.shmFile))
                {
                    Console.WriteLine("错误：从中间件获取的共享内存路径为空");
                    return null;
                }

                var sharedMemoryFilePath = responseData.data.shmFile;
                Console.WriteLine($"启动成功，共享内存文件路径: {sharedMemoryFilePath}");
                return sharedMemoryFilePath;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"启动中间层失败: {ex.Message}");
                return null;
            }
        }
    }
    public class MiddlewareResponseData
    {
        public string shmFile { get; set; }
    }

    public class MiddlewareResponse
    {
        public int code { get; set; }
        public string msg { get; set; }
        public MiddlewareResponseData data { get; set; }
    }
} 