﻿using System;
using System.Threading;
using System.Threading.Tasks;
using System.Diagnostics;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Linq;
using System.Collections.Concurrent;
using FXEtherCAT.SDK; // 引入SDKWrapper命名空间

namespace FXEtherCATControl
{
    public class AsyncLogger
    {
        private readonly string _logFilePath;
        private readonly ConcurrentQueue<string> _logQueue;
        private readonly CancellationTokenSource _cts;
        private Task _loggingTask;
        private readonly object _lockObject = new object();

        public AsyncLogger(string programName)
        {
            _logFilePath = Path.Combine("/tmp", $"{programName}.log");
            _logQueue = new ConcurrentQueue<string>();
            _cts = new CancellationTokenSource();
        }

        public void Start()
        {
            _loggingTask = Task.Run(ProcessLogQueue);
            Log($"日志服务启动 - {DateTime.Now}");
        }

        public void Stop()
        {
            _cts.Cancel();
            _loggingTask?.Wait();
            Log($"日志服务停止 - {DateTime.Now}");
        }

        public void Log(string message)
        {
            var logMessage = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}] {message}";
            _logQueue.Enqueue(logMessage);
        }

        private async Task ProcessLogQueue()
        {
            while (!_cts.Token.IsCancellationRequested)
            {
                try
                {
                    if (_logQueue.TryDequeue(out string message))
                    {
                        lock (_lockObject)
                        {
                            File.AppendAllText(_logFilePath, message + Environment.NewLine);
                        }
                    }
                    else
                    {
                        await Task.Delay(10, _cts.Token);
                    }
                }
                catch (Exception ex)
                {
                    var errorMessage = $"[ERROR] 日志处理错误: {ex.Message}";
                    lock (_lockObject)
                    {
                        File.AppendAllText(_logFilePath, errorMessage + Environment.NewLine);
                    }
                }
            }
        }
    }

    public class FXEtherCATController : IDisposable
    {
        private readonly FXEtherCATSDK _sdk;
        private readonly AsyncLogger _logger;
        private const int ENABLE_TIMEOUT_MS = 10000;

        public FXEtherCATController(AsyncLogger logger)
        {
            _logger = logger;
            _logger.Log($"Initializing FXEtherCATSDK...");
            try
            {
                _sdk = new FXEtherCATSDK();
                _logger.Log("FXEtherCATSDK initialization successful.");
            }
            catch (Exception ex)
            {
                _logger.Log($"FATAL: FXEtherCATSDK initialization failed: {ex.Message}");
                throw;
            }
        }

        private int GetStatusWord(ushort slaveIndex = 0)
        {
            if (!_sdk.ReadSlave(slaveIndex, 0x6041, 0, out int statusWord))
            {
                _logger.Log($"Warning: 读取 StatusWord (0x6041) 失败, slave {slaveIndex}");
                return 0;
            }
            return statusWord;
        }

        private void SetControlWord(ushort slaveIndex, int value)
        {
            if (!_sdk.WriteSlave(slaveIndex, 0x6040, 0, value))
            {
                _logger.Log($"Warning: 写入 ControlWord (0x6040) 失败, slave {slaveIndex}, value {value}");
            }
        }

        private void SetOperationMode(ushort slaveIndex, int value)
        {
            if (!_sdk.WriteSlave(slaveIndex, 0x6060, 0, value))
            {
                _logger.Log($"Warning: 写入 OperationMode (0x6060) 失败, slave {slaveIndex}, value {value}");
            }
        }

        public void SetTargetVelocity(ushort slaveIndex, int velocity)
        {
            if (!_sdk.WriteSlave(slaveIndex, 0x60FF, 0, velocity))
            {
                _logger.Log($"Warning: 写入 TargetVelocity (0x60FF) 失败, slave {slaveIndex}, value {velocity}");
            }
        }

        private bool GetSlaveOnlineStatus(ushort slaveIndex = 0)
        {
            if (_sdk.GetSlaveDeviceStatus(slaveIndex, out SlaveDeviceStatus status))
            {
                return status.IsOnline;
            }
            _logger.Log($"Warning: 获取 slave {slaveIndex} Online 状态失败");
            return false;
        }

        private bool GetSlaveOperationalStatus(ushort slaveIndex = 0)
        {
            if (_sdk.GetSlaveDeviceStatus(slaveIndex, out SlaveDeviceStatus status))
            {
                return status.IsOperational;
            }
            _logger.Log($"Warning: 获取 slave {slaveIndex} Operational 状态失败");
            return false;
        }

        private int GetAlState(ushort slaveIndex = 0)
        {
            if (_sdk.GetSlaveDeviceStatus(slaveIndex, out SlaveDeviceStatus status))
            {
                return status.AlState;
            }
            _logger.Log($"Warning: 获取 slave {slaveIndex} AL State 失败");
            return -1;
        }

        public bool EnableServo(ushort slaveIndex = 0)
        {
            _logger.Log($"开始使能从站 {slaveIndex}");
            Console.WriteLine($"开始使能从站 {slaveIndex}");

            // Set operation mode first
            SetOperationMode(slaveIndex, 9); // 9 = Cyclic Synchronous Velocity
            Thread.Sleep(50);

            // Attempt to clear any existing faults first, this moves state to "Switch On Disabled" from "Fault"
            SetControlWord(slaveIndex, 0x0080);
            Thread.Sleep(50);

            int elapsed = 0;
            while (elapsed < ENABLE_TIMEOUT_MS)
            {
                int statusWord = GetStatusWord(slaveIndex);

                // State machine logic based on CiA 402 state diagram
                if ((statusWord & 0x004F) == 0x0040) // State: "Switch on disabled"
                {
                    Console.WriteLine($"从站{slaveIndex} 状态: [Switch On Disabled], 发送控制字 0x0006 (Shutdown). 当前状态字: 0x{statusWord:X4}");
                    SetControlWord(slaveIndex, 0x0006);
                }
                else if ((statusWord & 0x006F) == 0x0021) // State: "Ready to switch on"
                {
                    Console.WriteLine($"从站{slaveIndex} 状态: [Ready to Switch On], 发送控制字 0x0007 (Switch On). 当前状态字: 0x{statusWord:X4}");
                    SetControlWord(slaveIndex, 0x0007);
                }
                else if ((statusWord & 0x006F) == 0x0023) // State: "Switched on"
                {
                    Console.WriteLine($"从站{slaveIndex} 状态: [Switched On], 发送控制字 0x000F (Enable Operation). 当前状态字: 0x{statusWord:X4}");
                    SetControlWord(slaveIndex, 0x000F);
                }
                else if ((statusWord & 0x006F) == 0x0027) // State: "Operation enabled"
                {
                    Console.WriteLine($"从站 {slaveIndex} 已成功使能. 最终状态字: 0x{statusWord:X4}");
                    _logger.Log($"从站 {slaveIndex} 已成功使能. 最终状态字: 0x{statusWord:X4}");
                    return true;
                }
                else if ((statusWord & 0x0008) != 0) // State: "Fault"
                {
                    Console.WriteLine($"从站{slaveIndex} 状态: [Fault], 尝试清除故障 (发送控制字 0x0080). 当前状态字: 0x{statusWord:X4}");
                    SetControlWord(slaveIndex, 0x0080); // Fault reset
                }
                else
                {
                    Console.WriteLine($"从站{slaveIndex} 处于未知或中间状态，等待... 当前状态字: 0x{statusWord:X4}");
                }

                Thread.Sleep(100);
                elapsed += 100;
            }

            _logger.Log($"从站 {slaveIndex} 使能超时，最终状态字: 0x{GetStatusWord(slaveIndex):X4}");
            Console.WriteLine($"从站 {slaveIndex} 使能超时，最终状态字: 0x{GetStatusWord(slaveIndex):X4}");
            return false;
        }

        public async Task RunControlSequence(CancellationToken cancellationToken)
        {
            _logger.Log("开始运行控制序列...");
            bool servoEnabled = false;
            bool wasOnline = false; // Initialize with current state

            try
            {
                while (!cancellationToken.IsCancellationRequested)
                {
                    bool isOnline = GetSlaveOnlineStatus(0);

                    if (isOnline != wasOnline)
                    {
                        if (isOnline)
                        {
                            _logger.Log("从站已上线，正在等待进入 OP 状态...");
                            Console.WriteLine("从站已上线，正在等待进入 OP 状态...");

                            // Wait for the slave to become operational
                            var opWaitStopwatch = Stopwatch.StartNew();
                            while (!GetSlaveOperationalStatus(0) && opWaitStopwatch.ElapsedMilliseconds < 5000 && !cancellationToken.IsCancellationRequested)
                            {
                                await Task.Delay(100, cancellationToken);
                            }

                            if (GetSlaveOperationalStatus(0))
                            {
                                _logger.Log("从站已进入 OP 状态，正在使能伺服...");
                                Console.WriteLine("从站已进入 OP 状态，正在使能伺服...");
                                servoEnabled = EnableServo(0);
                            }
                            else
                            {
                                _logger.Log("等待从站进入 OP 状态超时。");
                                Console.WriteLine("等待从站进入 OP 状态超时。");
                                servoEnabled = false;
                            }
                        }
                        else
                        {
                            _logger.Log("从站已离线，禁用伺服。");
                            Console.WriteLine("从站已离线，禁用伺服。");
                            DisableServo(0);
                            servoEnabled = false;
                        }
                        wasOnline = isOnline;
                    }

                    if (servoEnabled)
                    {
                        // Set velocity if servo is enabled
                        SetTargetVelocity(0, -100000000);
                    }

                    await Task.Delay(200, cancellationToken); // Main control loop interval
                }
            }
            catch (OperationCanceledException)
            {
                _logger.Log("控制序列被取消。");
                Console.WriteLine("控制序列被取消。");
            }
            catch (Exception ex)
            {
                _logger.Log($"控制序列出错: {ex.Message}");
                Console.WriteLine($"控制序列出错: {ex.Message}");
            }
            finally
            {
                _logger.Log("正在禁用伺服并清理...");
                Console.WriteLine("正在禁用伺服并清理...");
                if (GetSlaveOnlineStatus(0))
                {
                    DisableServo(0);
                }
            }
        }

        public void DisableServo(ushort slaveIndex = 0)
        {
            _logger.Log($"禁用伺服 (slave {slaveIndex})...");
            SetControlWord(slaveIndex, 0x0006);
            Thread.Sleep(100);
            SetControlWord(slaveIndex, 0x0000);
        }

        public void SetVelocity(int velocity, ushort slaveIndex = 0)
        {
            _logger.Log($"设置伺服速度: {velocity}");
            SetTargetVelocity(slaveIndex, velocity);
        }

        public void Dispose()
        {
            _logger.Log("释放 FXEtherCATController 资源...");
            _sdk?.Dispose();
        }
    }

    class Program
    {
        private static async Task<string> StartMiddleware(string programName, bool sdk)
        {
            try
            {
                using var client = new HttpClient();
                client.DefaultRequestHeaders.Accept.Add(
                    new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json")
                );

                var requestData = new { programName, sdk };
                var jsonContent = JsonSerializer.Serialize(requestData);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                Console.WriteLine($"正在请求EtherCAT中间层服务 ({programName})...");
                var response = await client.PostAsync(
                    "http://127.0.0.1:3000/api/programs/start-middleware", 
                    content
                );

                if (!response.IsSuccessStatusCode)
                {
                    Console.WriteLine($"请求中间件失败: {response.StatusCode}");
                    return null;
                }

                var responseContent = await response.Content.ReadAsStringAsync();
                Console.WriteLine($"中间件返回数据: {responseContent}");

                // 解析JSON响应
                var responseData = JsonSerializer.Deserialize<MiddlewareResponse>(responseContent);
                if (responseData.code != 0)
                {
                    string errorMsg = !string.IsNullOrEmpty(responseData.msg) ? responseData.msg : "未知错误";
                    Console.WriteLine($"错误：中间件返回错误码 {responseData.code}，错误信息：{errorMsg}");
                    return null;
                }

                if (responseData.data == null || string.IsNullOrEmpty(responseData.data.shmFile))
                {
                    Console.WriteLine("错误：从中间件获取的共享内存路径为空");
                    return null;
                }
                
                var sharedMemoryFilePath = responseData.data.shmFile;
                Console.WriteLine($"启动成功，共享内存文件路径: {sharedMemoryFilePath}");
                return sharedMemoryFilePath;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"启动中间层失败: {ex.Message}");
                return null;
            }
        }

        static async Task Main(string[] args)
        {
            AsyncLogger mainLogger = null;
            var cts = new CancellationTokenSource();

            // Set up graceful shutdown
            AppDomain.CurrentDomain.ProcessExit += (s, e) => {
                Console.WriteLine("收到退出信号，正在清理...");
                if (!cts.IsCancellationRequested)
                {
                    cts.Cancel();
                }
                // Give time for the finally block in RunControlSequence to execute
                Thread.Sleep(500);
            };

            Console.CancelKeyPress += (s, e) => {
                e.Cancel = true; // Prevent the process from terminating immediately
                Console.WriteLine("收到 Ctrl+C，正在优雅退出...");
                if (!cts.IsCancellationRequested)
                {
                    cts.Cancel();
                }
            };

            try
            {
                string programName = Path.GetFileName(Process.GetCurrentProcess().MainModule?.FileName ?? "unknown");
                mainLogger = new AsyncLogger(programName);
                mainLogger.Start();
                mainLogger.Log("程序启动");
                Console.WriteLine("FXEtherCAT 控制程序已启动。按下 Ctrl+C 退出。");
                
                await StartMiddleware(programName, true);

                using (var controller = new FXEtherCATController(mainLogger))
                {
                    // The RunControlSequence will now handle everything
                    await controller.RunControlSequence(cts.Token);
                }
            }
            catch (Exception ex)
            {
                mainLogger?.Log($"程序出现严重错误: {ex.Message}");
                Console.WriteLine($"程序出现严重错误: {ex.Message}");
            }
            finally
            {
                mainLogger?.Log("程序已停止。");
                mainLogger?.Stop();
                Console.WriteLine("程序已完全停止。");
            }
        }
    }

    public class MiddlewareResponseData
    {
        public string shmFile { get; set; }
    }

    public class MiddlewareResponse
    {
        public int code { get; set; }
        public string msg { get; set; }
        public MiddlewareResponseData data { get; set; }
    }
}