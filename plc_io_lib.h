#ifndef PLC_IO_LIB_H
#define PLC_IO_LIB_H

#ifdef __cplusplus
extern "C" {
#endif

#include <stdint.h>
#include <stdbool.h>

// 错误码定义
typedef enum {
    PLC_LIB_OK = 0,
    PLC_LIB_ERROR_INVALID_PARAM = -1,
    PLC_LIB_ERROR_NOT_INITIALIZED = -2,
    PLC_LIB_ERROR_NETWORK = -3,
    PLC_LIB_ERROR_TIMEOUT = -4,
    PLC_LIB_ERROR_REALTIME = -5,
    PLC_LIB_ERROR_MEMORY = -6
} plc_lib_result_t;

// 配置结构体
typedef struct {
    char ip_address[64];        // PLC设备IP地址
    int port;                   // PLC设备端口
    int timeout_ms;             // 通信超时时间(毫秒)
    int cpu_core;               // 绑定的CPU核心 (-1表示不绑定)
    int rt_priority;            // 实时优先级 (1-99, 0表示不设置实时)
    bool enable_memory_lock;    // 是否启用内存锁定
    int io_input_count;         // 输入IO数量
    int io_output_count;        // 输出IO数量
} plc_config_t;

// 信号灯配置结构体
typedef struct {
    int red_light_io;           // 红灯IO地址
    int green_light_io;         // 绿灯IO地址
    int yellow_light_io;        // 黄灯IO地址
    int buzzer_io;              // 蜂鸣器IO地址
} signal_light_config_t;

// 信号灯状态枚举
typedef enum {
    SIGNAL_OFF = 0,             // 关闭
    SIGNAL_ON = 1,              // 常亮
    SIGNAL_BLINK = 2            // 闪烁
} signal_state_t;

// 统计信息结构体
typedef struct {
    uint64_t total_requests;    // 总请求数
    uint64_t success_requests;  // 成功请求数
    uint64_t failed_requests;   // 失败请求数
    uint64_t timeout_count;     // 超时次数
    double avg_latency_ms;      // 平均延迟(毫秒)
    double max_latency_ms;      // 最大延迟(毫秒)
    double min_latency_ms;      // 最小延迟(毫秒)
    bool is_connected;          // 连接状态
    int current_rt_priority;    // 当前实时优先级
    int current_cpu_core;       // 当前绑定的CPU核心
} plc_stats_t;

/**
 * 初始化PLC IO库
 * @param config 配置参数
 * @return 操作结果
 */
plc_lib_result_t plc_init(const plc_config_t* config);

/**
 * 连接到PLC设备
 * @return 操作结果
 */
plc_lib_result_t plc_connect(void);

/**
 * 断开PLC连接
 * @return 操作结果
 */
plc_lib_result_t plc_disconnect(void);

/**
 * 读取所有IO状态
 * @param input_data 输入IO数据缓冲区 (调用者分配)
 * @param input_size 输入缓冲区大小
 * @param output_data 输出IO数据缓冲区 (调用者分配)
 * @param output_size 输出缓冲区大小
 * @param actual_input_count 实际读取的输入IO数量
 * @param actual_output_count 实际读取的输出IO数量
 * @return 操作结果
 */
plc_lib_result_t plc_read_all_io(uint8_t* input_data, int input_size,
                                 uint8_t* output_data, int output_size,
                                 int* actual_input_count, int* actual_output_count);

/**
 * 读取单个输入IO
 * @param io_index IO索引 (0开始)
 * @param value 输出值 (0或1)
 * @return 操作结果
 */
plc_lib_result_t plc_read_input(int io_index, uint8_t* value);

/**
 * 写入单个输出IO
 * @param io_index IO索引 (0开始)
 * @param value 输出值 (0或1)
 * @return 操作结果
 */
plc_lib_result_t plc_write_output(int io_index, uint8_t value);

/**
 * 获取统计信息
 * @param stats 统计信息输出
 * @return 操作结果
 */
plc_lib_result_t plc_get_stats(plc_stats_t* stats);

/**
 * 重置统计信息
 * @return 操作结果
 */
plc_lib_result_t plc_reset_stats(void);

/**
 * 获取错误描述
 * @param error_code 错误码
 * @return 错误描述字符串
 */
const char* plc_get_error_string(plc_lib_result_t error_code);

/**
 * 检查连接状态
 * @return true表示已连接
 */
bool plc_is_connected(void);

/**
 * 清理资源并退出
 * @return 操作结果
 */
plc_lib_result_t plc_cleanup(void);

/**
 * 验证实时配置是否生效
 * @param actual_cpu_core 实际绑定的CPU核心 (-1表示未绑定)
 * @param actual_rt_priority 实际实时优先级 (0表示非实时)
 * @param actual_policy 实际调度策略 (0=SCHED_OTHER, 1=SCHED_FIFO, 2=SCHED_RR)
 * @param memory_locked 内存是否已锁定
 * @return 操作结果
 */
plc_lib_result_t plc_verify_realtime_config(int* actual_cpu_core,
                                           int* actual_rt_priority,
                                           int* actual_policy,
                                           bool* memory_locked);

/**
 * 显示当前进程的详细实时状态
 * @return 操作结果
 */
plc_lib_result_t plc_show_process_info(void);

// ==================== LED和蜂鸣器控制API ====================

/**
 * 配置LED和蜂鸣器IO地址
 * @param config 信号灯配置
 * @return 操作结果
 */
plc_lib_result_t plc_config_signal_lights(const signal_light_config_t* config);

/**
 * 设置LED和蜂鸣器状态
 * @param red_state 红灯状态 (0=关闭, 1=常亮, 2=闪烁)
 * @param green_state 绿灯状态
 * @param yellow_state 黄灯状态
 * @param buzzer_state 蜂鸣器状态
 * @param blink_interval_ms 闪烁间隔时间(毫秒)，仅对状态为2的信号有效，0表示立即设置
 * @param blink_count 闪烁次数 (0=无限循环, >0=指定次数后停止)
 * @return 操作结果
 */
plc_lib_result_t set_led_and_buzzer(signal_state_t red_state,
                                   signal_state_t green_state,
                                   signal_state_t yellow_state,
                                   signal_state_t buzzer_state,
                                   uint32_t blink_interval_ms,
                                   uint32_t blink_count);

/**
 * 输入变化回调函数类型
 * @param io_index IO索引 (0开始)
 * @param new_value 新的值 (0或1)
 * @param timestamp_us 微秒时间戳
 */
typedef void (*input_change_callback_t)(int io_index, uint8_t new_value, uint64_t timestamp_us);

/**
 * 注册输入变化回调函数
 * @param callback 回调函数指针
 * @return 操作结果
 */
plc_lib_result_t plc_register_input_callback(input_change_callback_t callback);

/**
 * 注销输入变化回调函数
 * @return 操作结果
 */
plc_lib_result_t plc_unregister_input_callback(void);

/**
 * 启动输入监控
 * @return 操作结果
 */
plc_lib_result_t plc_start_input_monitoring(void);

/**
 * 停止输入监控
 * @return 操作结果
 */
plc_lib_result_t plc_stop_input_monitoring(void);

#ifdef __cplusplus
}
#endif

#endif // PLC_IO_LIB_H
