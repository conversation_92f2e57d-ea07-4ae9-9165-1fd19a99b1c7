#!/usr/bin/env python3
import re
import json

def parse_log_file(filename):
    """解析日志文件，提取关键时间节点"""
    
    # 正则表达式匹配时间节点
    pattern = r'\[([^\]]+)\] \(内核启动后 ([\d.]+) 秒\) - (.+)'
    
    # 正则表达式匹配轮次开始
    round_pattern = r'第 (\d+) 轮开机自恢复测试开始'
    
    timeline_data = {}
    current_round = 0
    
    with open(filename, 'r', encoding='utf-8') as f:
        for line_num, line in enumerate(f, 1):
            line = line.strip()
            
            # 检查是否是新轮次开始
            round_match = re.search(round_pattern, line)
            if round_match:
                current_round = int(round_match.group(1))
                timeline_data[current_round] = {}
            
            # 匹配时间节点
            match = re.match(pattern, line)
            if match and current_round > 0:
                timestamp = match.group(1)
                kernel_time = float(match.group(2))
                description = match.group(3)
                
                # 提取关键节点
                if '轮开机自恢复测试开始' in description:
                    timeline_data[current_round]['test_start'] = {
                        'timestamp': timestamp,
                        'kernel_time': kernel_time,
                        'description': description
                    }
                elif '脚本实际启动' in description:
                    timeline_data[current_round]['kernel_start'] = {
                        'timestamp': timestamp,
                        'kernel_time': kernel_time,
                        'description': description
                    }
                elif '设备 /dev/EtherCAT0 已找到' in description:
                    timeline_data[current_round]['driver_loaded'] = {
                        'timestamp': timestamp,
                        'kernel_time': kernel_time,
                        'description': description
                    }
                elif '服务 ethercat-web-ui.service 已成功启动' in description:
                    timeline_data[current_round]['service_started'] = {
                        'timestamp': timestamp,
                        'kernel_time': kernel_time,
                        'description': description
                    }
                elif '检测到从站已进入 PREOP 状态' in description:
                    timeline_data[current_round]['preop_reached'] = {
                        'timestamp': timestamp,
                        'kernel_time': kernel_time,
                        'description': description
                    }
                elif '检测到从站已进入 OP 状态' in description:
                    timeline_data[current_round]['op_reached'] = {
                        'timestamp': timestamp,
                        'kernel_time': kernel_time,
                        'description': description
                    }
                elif '伺服已成功使能' in description:
                    timeline_data[current_round]['servo_enabled'] = {
                        'timestamp': timestamp,
                        'kernel_time': kernel_time,
                        'description': description
                    }
                elif '本轮测试总耗时' in description:
                    timeline_data[current_round]['test_completed'] = {
                        'timestamp': timestamp,
                        'kernel_time': kernel_time,
                        'description': description
                    }
    
    return timeline_data

def generate_summary_table(timeline_data):
    """生成汇总表格"""

    markdown_content = """# 开机自恢复测试关键时间节点汇总表

## 测试概览
本文档汇总了26轮开机自恢复测试的关键时间节点，便于快速查看和分析测试性能。

## 关键节点说明
- **测试开始**: 每轮测试开始的时间
- **内核启动**: Linux内核开机时间（从内核启动开始计时）
- **驱动加载**: EtherCAT驱动加载完成时间
- **服务启动**: Web UI服务启动完成时间
- **PREOP状态**: 第一个从站进入PREOP状态时间
- **OP状态**: 第一个从站进入OP状态时间
- **伺服使能**: 第一个伺服成功使能时间
- **测试完成**: 本轮测试完成时间

## 汇总表格

| 轮次 | 测试开始时间 | 内核启动(秒) | 驱动加载(秒) | 服务启动(秒) | PREOP(秒) | OP状态(秒) | 伺服使能(秒) | 测试完成(秒) |
|------|-------------|-------------|-------------|-------------|-----------|-----------|-------------|-------------|
"""
    
    for round_num in sorted(timeline_data.keys()):
        events = timeline_data[round_num]
        if not events:
            continue
        
        # 提取各个关键时间点
        test_start = events.get('test_start', {}).get('timestamp', '-')
        kernel_start = events.get('kernel_start', {}).get('kernel_time', '-')
        driver_loaded = events.get('driver_loaded', {}).get('kernel_time', '-')
        service_started = events.get('service_started', {}).get('kernel_time', '-')
        preop_reached = events.get('preop_reached', {}).get('kernel_time', '-')
        op_reached = events.get('op_reached', {}).get('kernel_time', '-')
        servo_enabled = events.get('servo_enabled', {}).get('kernel_time', '-')
        test_completed = events.get('test_completed', {}).get('kernel_time', '-')

        markdown_content += f"| {round_num} | {test_start} | {kernel_start} | {driver_loaded} | {service_started} | {preop_reached} | {op_reached} | {servo_enabled} | {test_completed} |\n"
    
    # 添加统计分析
    markdown_content += """
## 性能分析

### 各阶段耗时统计
下表显示了从内核启动到各个关键节点的平均时间：

"""
    
    # 计算统计数据
    stats = {}
    for round_num, events in timeline_data.items():
        for key, event in events.items():
            if key not in stats:
                stats[key] = []
            if 'kernel_time' in event:
                stats[key].append(event['kernel_time'])
    
    markdown_content += "| 阶段 | 平均时间(秒) | 最短时间(秒) | 最长时间(秒) | 标准差 |\n"
    markdown_content += "|------|-------------|-------------|-------------|--------|\n"
    
    stage_names = {
        'kernel_start': '内核启动',
        'driver_loaded': '驱动加载',
        'service_started': '服务启动',
        'preop_reached': '第一个从站PREOP',
        'op_reached': '第一个从站OP',
        'servo_enabled': '第一个伺服使能',
        'test_completed': '测试完成'
    }
    
    for key, times in stats.items():
        if times and key in stage_names:
            avg_time = sum(times) / len(times)
            min_time = min(times)
            max_time = max(times)
            std_dev = (sum((x - avg_time) ** 2 for x in times) / len(times)) ** 0.5
            
            markdown_content += f"| {stage_names[key]} | {avg_time:.1f} | {min_time:.1f} | {max_time:.1f} | {std_dev:.1f} |\n"
    
    return markdown_content

def main():
    # 解析日志文件
    timeline_data = parse_log_file('power_on_test.log')
    
    # 生成汇总表格
    markdown_content = generate_summary_table(timeline_data)
    
    # 写入文件
    with open('power_on_test_summary.md', 'w', encoding='utf-8') as f:
        f.write(markdown_content)
    
    print(f"汇总表格生成完成！共处理了 {len(timeline_data)} 轮测试")

if __name__ == "__main__":
    main()
